import 'package:vp_common/vp_common.dart';

enum FavoristType {
  stock,
  derivatives,
  depositTransfer,
  investmentProducts,
  assetsStatement,
  marketInfo,
  otherUtilities,
  support,
}

// C<PERSON> phiếu
// Phái sinh
// Nạp tiền/ Chuyển tiền
// Sản phẩm đầu tư
// Tài sản/ Sao kê
// Thông tin thị trường
// Tiện ích khác
// Hỗ trợ
extension FavoristTypeToString on FavoristType {
  String get key {
    switch (this) {
      case FavoristType.stock:
        return 'STOCK';
      case FavoristType.derivatives:
        return 'DERIVATIVES';
      case FavoristType.depositTransfer:
        return 'DEPOSIT_TRANSFER';
      case FavoristType.investmentProducts:
        return 'INVESTMENT_PRODUCTS';
      case FavoristType.assetsStatement:
        return 'ASSETS_STATEMENT';
      case FavoristType.marketInfo:
        return 'MARKET_INFO';
      case FavoristType.otherUtilities:
        return 'OTHER_UTILITIES';
      case FavoristType.support:
        return 'SUPPORT';
    }
  }

  String get label {
    switch (this) {
      case FavoristType.stock:
        return 'Cổ phiếu';
      case FavoristType.derivatives:
        return 'Phái sinh';
      case FavoristType.depositTransfer:
        return 'Nạp tiền/ Chuyển tiền';
      case FavoristType.investmentProducts:
        return 'Sản phẩm đầu tư';
      case FavoristType.assetsStatement:
        return 'Tài sản/ Sao kê';
      case FavoristType.marketInfo:
        return 'Thông tin thị trường';
      case FavoristType.otherUtilities:
        return 'Tiện ích';
      case FavoristType.support:
        return 'Hỗ trợ';
    }
  }

  // String get title {
  //   switch (this) {
  //     case FavoristType.stockUtility:
  //       return VPNeoLocalize.current.home_stock_utility;
  //     case FavoristType.invest:
  //       return VPNeoLocalize.current.home_stock_invest;
  //     case FavoristType.derivatives:
  //       return VPNeoLocalize.current.home_stock_derivatives;
  //     case FavoristType.other:
  //       return VPNeoLocalize.current.home_stock_other;
  //   }
  // }

  static FavoristType fromJson(String? value) {
    switch (value) {
      case 'stock':
        return FavoristType.stock;
      case 'derivatives':
        return FavoristType.derivatives;
      case 'depositTransfer':
        return FavoristType.depositTransfer;
      case 'investmentProducts':
        return FavoristType.investmentProducts;
      case 'assetsStatement':
        return FavoristType.assetsStatement;
      case 'marketInfo':
        return FavoristType.marketInfo;
      case 'otherUtilities':
        return FavoristType.otherUtilities;
      case 'support':
        return FavoristType.support;
      default:
        return FavoristType.otherUtilities; // fallback
    }
  }
}

class FavoriteModel extends Equatable {
  FavoriteModel({
    required this.title,
    required this.id,
    required this.route,
    required this.favoristType,
    required this.path,
    this.arguments,
  });

  factory FavoriteModel.none() {
    return FavoriteModel(
      title: '',
      id: '',
      path: '',
      route: '',
      favoristType: FavoristType.otherUtilities,
    );
  }

  factory FavoriteModel.fromJson(Map<String, dynamic> json) {
    return FavoriteModel(
      route: json['route'] as String,
      title: json['title'] as String,
      id: json['id'] as String,
      path: json['path'] as String,
      arguments: json['arguments'],
      favoristType: FavoristTypeToString.fromJson(
        json['favoristType'] as String,
      ),
    );
  }

  bool get isNone => title.isEmpty && id.isEmpty && route.isEmpty;
  String title;
  String id;
  String path;
  String route;
  Object? arguments;
  FavoristType favoristType;

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'id': id,
      'path': path,
      'favoristType': favoristType.key,
      'route': route,
      'arguments': arguments,
    };
  }

  @override
  List<Object?> get props => [title, id, path, favoristType, route, arguments];
}

//       1. “Đặt lệnh CP”
// 2. "Đặt lệnh PS"
// 3. “Nạp tiền”
// 4. “Chuyển tiền”
// 5. “ePortfolio”
// 6. “Chứng chỉ quỹ”
// 7. “Dịch vụ tài chính”
// 8. “SmartOTP”
List<FavoriteModel> mockFeturesReponse = [
  // Tiện ích cổ phiếu
  FavoriteModel(
    title: 'Đặt lệnh CP',
    id: '1',
    path: 'ic_datlenhcp',
    favoristType: FavoristType.stock,
    route: '/placeOrder',
  ),
  FavoriteModel(
    title: 'Danh mục CP',
    id: '2',
    path: 'ic_danhmuc',
    favoristType: FavoristType.stock,
    route: '/mainStockHome?initialPage=1',
  ),
  FavoriteModel(
    title: 'Sổ lệnh CP',
    id: '3',
    path: 'ic_solenh',
    favoristType: FavoristType.stock,
    arguments: 2,
    route: '/mainStockHome?initialPage=2',
  ),
  FavoriteModel(
    title: 'Dịch vụ tài chính',
    id: '4',
    path: 'ic_dinhvutaichinh',
    favoristType: FavoristType.stock,
    route: '/financialServicePackage',
  ),
  FavoriteModel(
    title: 'Bộ lọc cổ phiếu',
    id: '5',
    path: 'ic_boloccophieu',
    favoristType: FavoristType.stock,
    route: '/stockFilter',
  ),
  FavoriteModel(
    title: 'Chuyển khoản chứng khoán',
    id: '6',
    path: 'ic_chuyekhoanck',
    favoristType: FavoristType.stock,
    route: '/stockTransfer',
  ),
  FavoriteModel(
    title: 'Đăng ký quyền mua',
    id: '7',
    path: 'ic_dangkyquyenmua',
    favoristType: FavoristType.stock,
    route: '/stockRight',
  ),
  FavoriteModel(
    title: 'Xác nhận lệnh',
    id: '8',
    path: 'ic_xacnhan',
    favoristType: FavoristType.stock,
    route: '/orderConfirm',
  ),

  FavoriteModel(
    title: 'Đặt lệnh PS',
    id: '9',
    path: 'ic_datlenhps',
    favoristType: FavoristType.derivatives,
    route: '/tradingDerivativePage',
  ),
  FavoriteModel(
    title: 'Sổ lệnh PS',
    id: '10',
    path: 'ic_solenh',
    favoristType: FavoristType.derivatives,
    route: '/derivativesOrderBookScreen',
  ),
  FavoriteModel(
    title: 'Danh mục vị thế',
    id: '11',
    path: 'ic_danhmuc',
    favoristType: FavoristType.derivatives,
    route: '/derivatives_portfolio',
  ),
  FavoriteModel(
    title: 'Lịch sử GD PS',
    id: '12',
    path: 'ic_lichsu',
    favoristType: FavoristType.derivatives,
    route: '/derivativeTransactionHistory',
  ),

  FavoriteModel(
    title: 'Chuyển tiền',
    id: '13',
    path: 'ic_chuyentien',
    favoristType: FavoristType.depositTransfer,
    route: '/moneyTranfer',
  ),
  FavoriteModel(
    title: 'Nạp tiền',
    id: '14',
    path: 'ic_naptien',
    favoristType: FavoristType.depositTransfer,
    route: '/moneyCashIn',
  ),
  FavoriteModel(
    title: 'Nộp/Rút ký quỹ PS',
    id: '15',
    path: 'ic_nopkiquyps',
    favoristType: FavoristType.depositTransfer,
    route: '/derivativeTransaction',
  ),
  FavoriteModel(
    title: 'Ứng trước tiền bán',
    id: '16',
    path: 'ic_ungtruoctienban',
    favoristType: FavoristType.depositTransfer,
    route: '/advancePayment',
  ),
  FavoriteModel(
    title: 'Sao kê tiền',
    id: '17',
    path: 'ic_saoketien',
    favoristType: FavoristType.depositTransfer,
    route: '/moneyStatement',
  ),
  FavoriteModel(
    title: 'Lịch sử chuyển tiền',
    id: '18',
    path: 'ic_lichsu',
    favoristType: FavoristType.depositTransfer,
    route: '/moneyTransHistory',
  ),
  FavoriteModel(
    title: 'Thông tin thị trường',
    id: '19',
    path: 'ic_thongtinthitruong',
    favoristType: FavoristType.marketInfo,
    route: '/market',
  ),
  FavoriteModel(
    title: 'Lịch sự kiện',
    id: '20',
    path: 'ic_lichsukien',
    favoristType: FavoristType.marketInfo,
    route: '/event',
  ),
  FavoriteModel(
    title: 'Xếp hạng ngành/CP',
    id: '21',
    path: 'ic_xephang',
    favoristType: FavoristType.marketInfo,
    route: '/ranking',
  ),
  FavoriteModel(
    title: 'Khuyến nghị đầu tư',
    id: '22',
    path: 'ic_khuyennghidautu',
    favoristType: FavoristType.marketInfo,
    route: '/recommendationList',
  ),
  FavoriteModel(
    title: 'Cảnh báo cổ phiếu',
    id: '23',
    path: 'ic_canhbao',
    favoristType: FavoristType.marketInfo,
    route: '/stockAlert',
  ),
  FavoriteModel(
    title: 'StockGuru',
    id: '24',
    path: 'ic_neoai',
    favoristType: FavoristType.marketInfo,
    route: '/code_pilot_get_token',
  ),

  FavoriteModel(
    title: 'ePortfolio',
    id: '25',
    path: 'ic_eport',
    favoristType: FavoristType.investmentProducts,
    route: '/dashBoard',
  ),
  FavoriteModel(
    title: 'Chứng chỉ quỹ',
    id: '26',
    path: 'ic_ccquy',
    favoristType: FavoristType.investmentProducts,
    route: '/fundMainPage',
  ),
  FavoriteModel(
    title: 'Trái phiếu',
    id: '27',
    path: 'ic_traiphieu',
    favoristType: FavoristType.investmentProducts,
    route: '/bondManagerPage',
  ),
  FavoriteModel(
    title: 'Tích sản',
    id: '28',
    path: 'ic_tichsan',
    favoristType: FavoristType.investmentProducts,
    route: '/checkCurrentStatusWealth',
  ),
  FavoriteModel(
    title: 'Tài sản',
    id: '29',
    path: 'ic_taisan',
    favoristType: FavoristType.assetsStatement,
    route: '/assetOverviewFromFavorite',
  ),
  FavoriteModel(
    title: 'Thống kê tài sản',
    id: '30',
    path: 'ic_danhmuc',
    favoristType: FavoristType.assetsStatement,
    route: '/assetCollectionFromFavorite',
  ),
  FavoriteModel(
    title: 'Sao kê tiền',
    id: '31',
    path: 'ic_saoketien',
    favoristType: FavoristType.assetsStatement,
    route: '/moneyStatementFromFavorite',
  ),
  FavoriteModel(
    title: 'Sao kê Chứng khoán',
    id: '32',
    path: 'ic_saokeck',
    favoristType: FavoristType.assetsStatement,
    route: '/securitiesStatement',
  ),
  FavoriteModel(
    title: 'Sao kê ký quỹ phái sinh',
    id: '33',
    path: 'ic_saokekyquyps',
    favoristType: FavoristType.assetsStatement,
    route: '/derivativeStatement',
  ),
  FavoriteModel(
    title: 'Lấy mã Smart OTP',
    id: '34',
    path: 'ic_smart_otp',
    favoristType: FavoristType.otherUtilities,
    route: '/checkSmartOTPPage',
  ),
  FavoriteModel(
    title: 'Cài đặt Bảo mật',
    id: '35',
    path: 'ic_baomat',
    favoristType: FavoristType.otherUtilities,
    route: '/settingSecurity',
  ),
  FavoriteModel(
    title: 'Khách hàng thân thiết',
    id: '36',
    path: 'ic_loyalty',
    favoristType: FavoristType.otherUtilities,
    route: '/loyalty',
  ),
  FavoriteModel(
    title: 'Giới thiệu bạn mới',
    id: '37',
    path: 'ic_gioithieu',
    favoristType: FavoristType.otherUtilities,
    route: '/shareReferralPage',
  ),
  FavoriteModel(
    title: 'Hỗ trợ',
    id: '38',
    path: 'ic_hotro',
    favoristType: FavoristType.support,
    route: '/settingSupport',
  ),
  //TODO: Not implement
  // FavoriteModel(
  //   title: 'Hướng dẫn sử dụng',
  //   id: '39',
  //   path: 'ic_hotro',
  //   favoristType: FavoristType.support,
  //   route: '',
  // ),
];
