import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:new_neo_invest/gen/assets.gen.dart' as bond;
import 'package:new_neo_invest/screen/check_current_status_wealth/check_current_status_wealth_mixin.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/cubit/auth_cubit.dart';
import 'package:vp_core/firebase/remote_config/remote_config_service.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_finvest/generated/assets.gen.dart';
import 'package:vp_fund/generated/assets.gen.dart';
import 'package:vp_fund/generated/l10n.dart';
import 'package:vp_portfolio/generated/assets.gen.dart';
import 'package:vp_wealth/generated/assets.gen.dart' as wealth;

class InvestNavigate extends StatelessWidget with CheckCurrentStatusWealth {
  const InvestNavigate({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      clipBehavior: Clip.hardEdge,
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      decoration: BoxDecoration(
        color: themeData.bgPopup,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),
            Center(
              child: Container(
                width: 56,
                height: 8,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: themeData.buttonTopBottomSheet,
                ),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Đầu tư',
              style: vpTextStyle.subtitle16.copyColor(vpColor.textPrimary),
            ),
            ListView(
              shrinkWrap: true,
              padding: EdgeInsets.zero,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: ItemInvestNavigate(
                        image: bond.Assets.icons.icBond,
                        text: 'Trái phiếu',
                        onTap: () => context
                          ..pop()
                          ..push('/bondManagerPage'),
                      ),
                    ),
                    Expanded(
                      child: ItemInvestNavigate(
                        image: VpPortfolioAssets.icons.icEPortfolio.path,
                        package: VpPortfolioAssets.package,
                        text: 'ePortfolio',
                        onTap: () => context
                          ..pop()
                          ..push('/dashBoard'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ItemInvestNavigate(
                        image: VpFundAssets.icons.icFund.path,
                        package: VpFundAssets.package,
                        text: VPFundLocalize.current.efund,
                        onTap: () => context
                          ..pop()
                          ..push('/fundMainPage'),
                      ),
                    ),
                    StreamBuilder<bool>(
                        stream:
                            RemoteConfigService().getShowWealth().asStream(),
                        builder: (context, snapshot) {
                          if (snapshot.hasData && (snapshot.data ?? false)) {
                            final iamInfo =
                                GetIt.instance<AuthCubit>().customerInfoIam;
                            return (iamInfo?.type == 'I' &&
                                    iamInfo?.investorType == '001')
                                ? Expanded(
                                    child: ItemInvestNavigate(
                                      image: wealth.Assets.icons.icWealth.path,
                                      package: wealth.Assets.package,
                                      text: 'Tích sản',
                                      onTap: () {
                                        checkCurrentStatusWealth(context);
                                      },
                                    ),
                                  )
                                : const SizedBox.shrink();
                          }
                          return const SizedBox.shrink();
                        })
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ItemInvestNavigate(
                        image: VpFinvestAssets.icons.icFinvest.path,
                        package: VpFinvestAssets.package,
                        text: 'Finvest',
                        onTap: () => context
                          ..pop()
                          ..push('/finvest'),
                      ),
                    ),
                    const Spacer()
                  ],
                ),
              ],
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}

class ItemInvestNavigate extends StatelessWidget {
  const ItemInvestNavigate({
    required this.image,
    required this.text,
    this.package,
    super.key,
    this.onTap,
  });

  final String image;
  final String? package;
  final String text;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: InkWell(
        onTap: onTap,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppIconBg(
              icon: SvgPicture.asset(
                image,
                package: package,
              ),
              size: 56,
              padding: 14,
            ),
            const SizedBox(height: 8),
            Text(
              text,
              style: vpTextStyle.subtitle14.copyColor(themeData.black),
            ),
          ],
        ),
      ),
    );
  }
}
