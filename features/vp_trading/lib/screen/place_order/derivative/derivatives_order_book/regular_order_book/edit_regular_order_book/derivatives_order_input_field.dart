import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

/// Reusable input field widget với +/- buttons cho derivatives order dialog
class DerivativesOrderInputField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final FocusNode focusNode;
  final VoidCallback onDecrease;
  final VoidCallback onIncrease;
  final ValueChanged<String> onChanged;
  final List<TextInputFormatter> inputFormatters;
  final TextInputType keyboardType;
  final String? errorText;
  final bool enabled;

  const DerivativesOrderInputField({
    super.key,
    required this.controller,
    required this.label,
    required this.focusNode,
    required this.onDecrease,
    required this.onIncrease,
    required this.onChanged,
    required this.inputFormatters,
    required this.keyboardType,
    this.errorText,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                label,
                style: context.textStyle.body14?.copyWith(
                  color: context.colors.textSecondary,
                ),
              ),
            ),
            const SizedBox(width: 16),
            SizedBox(
              width: 200,
              child: VPTextField(
                controller: controller,
                textAlign: TextAlign.center,
                keyboardType: keyboardType,
                focusNode: focusNode,
                inputFormatters: inputFormatters,
                onChanged: enabled ? onChanged : null,
                readOnly: !enabled,
                inputType:
                    errorText != null
                        ? InputType.error
                        : !enabled
                        ? InputType.disabled
                        : InputType.rest,
                style: context.textStyle.body14?.copyWith(
                  color:
                      enabled
                          ? context.colors.textPrimary
                          : context.colors.textDisabled,
                ),
                prefixIcon:
                    (color) => IconButton(
                      padding: const EdgeInsets.all(0),
                      icon: Icon(
                        Icons.remove,
                        color:
                            enabled
                                ? context.colors.iconPrimary
                                : context.colors.iconDisabled,
                        size: 16,
                      ),
                      onPressed: enabled ? onDecrease : null,
                    ),
                suffixIcon:
                    (color) => IconButton(
                      padding: const EdgeInsets.all(0),
                      icon: Icon(
                        Icons.add,
                        color:
                            enabled
                                ? context.colors.iconPrimary
                                : context.colors.iconDisabled,
                        size: 16,
                      ),
                      onPressed: enabled ? onIncrease : null,
                    ),
                caption:
                    errorText != null
                        ? (color) => Text(
                          errorText!,
                          style: context.textStyle.captionRegular?.copyWith(
                            color: context.colors.textAccentRed,
                          ),
                        )
                        : null,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
