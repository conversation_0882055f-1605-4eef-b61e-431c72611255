import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/constant/derivative_app_configs.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/core/constant/time_type.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/cubit/derivative/validate_order/derivative_validate_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/submit/place_order_submit_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/enum/activation_conditions_type.dart';
import 'package:vp_trading/model/enum/market_type.dart';
import 'package:vp_trading/model/enum/order_trading_type.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/model/order/request/order_request_model.dart';
import 'package:vp_trading/screen/place_order/widgets/confirm/place_derivative_condition_order_confirm_dialog.dart';
import 'package:vp_trading/screen/place_order/widgets/confirm/place_order_confirm_dialog.dart';

mixin OrderOptionalTypeMixin {
  Future<void> orderRegular(
    BuildContext context,
    OrderAction action,
    Function() updateUserConfig,
  ) async {
    final symbol = context.read<PlaceOrderCubit>().state.symbol;
    final side = action.value;
    final calculateValue =
        context.read<DerivativeValidateOrderCubit>().state.calculateValue;
    final username =
        GetIt.instance<AuthCubit>().userInfo?.userinfo?.username ?? '';
    final currentVolume =
        context.read<DerivativeValidateOrderCubit>().state.currentVolume ?? "0";
    final currentPrice =
        context.read<DerivativeValidateOrderCubit>().state.currentPrice ?? "0";
    final subAccountType = context.read<PlaceOrderCubit>().state.subAccountType;
    final sessionType =
        context.read<DerivativeValidateOrderCubit>().state.sessionType;
    final orderTradingType =
        sessionType != null
            ? OrderTradingType.market.nameServer
            : OrderTradingType.limit.nameServer;
    final price =
        sessionType != null
            ? sessionType.name.toUpperCase()
            : currentPrice.priceDerivative ?? 0;
    final subAccountId = subAccountType.toSubAccountModel()?.id ?? "";

    // Validate volume
    context.read<DerivativeValidateOrderCubit>().onValidateVolume(
      currentVolume,
    );
    final errorPrice =
        context.read<DerivativeValidateOrderCubit>().state.errorPrice;
    final errorVolume =
        context.read<DerivativeValidateOrderCubit>().state.errorVolume;
    if (errorPrice == ErrorPrice.empty) {
      showMessageError("Chưa nhập giá!");
      return;
    }
    if (errorVolume == ErrorVolume.empty) {
      showMessageError("Chưa nhập khối lượng!");
      return;
    }
    if (errorVolume.isError || errorPrice.isError) return;

    // Create request
    final request = OrderRequestModel(
      qty: (currentVolume.volume).toInt(),
      price: price,
      symbol: symbol,
      market: MarketType.derivatives.nameServer,
      side: side,
      type: orderTradingType,
      username: username,
      accountId: subAccountId,
    );

    if (DerivativeAppConfigs().userConfig.isShowNotifyConfirmOrder) {
      await showDialog(
        context: context,
        builder:
            (_) => PlaceOrderConfirmDialog(
              price: sessionType?.name ?? currentPrice,
              value: calculateValue,
              requestModel: request,
              isDerivative: true,
              onConfirm: (showConfirmOrder) {
                DerivativeAppConfigs().userConfig.setNotifyConfirmOrder(
                  showConfirmOrder,
                );
                updateUserConfig();
                context.read<PlaceOrderSubmitCubit>().submitPlaceOrder(
                  request: request,
                );
              },
            ),
      );
    } else {
      context.read<PlaceOrderSubmitCubit>().submitPlaceOrder(request: request);
    }
  }

  Future<void> orderCondition(
    BuildContext context,
    OrderAction action,
    DerivativeOrderOptionalType orderType,
    Function() updateUserConfig,
  ) async {
    final symbol = context.read<PlaceOrderCubit>().state.symbol;
    final side = action.value;
    final currentVolume =
        context.read<DerivativeValidateOrderCubit>().state.currentVolume ?? "0";
    final currentPrice =
        context.read<DerivativeValidateOrderCubit>().state.currentPrice ?? "0";
    final subAccountType = context.read<PlaceOrderCubit>().state.subAccountType;
    final sessionType =
        context.read<DerivativeValidateOrderCubit>().state.sessionType;
    final price =
        sessionType != null
            ? sessionType.name.toUpperCase()
            : currentPrice.priceDerivative ?? 0;
    final subAccountId = subAccountType.toSubAccountModel()?.id ?? "";

    // Validate volume
    context.read<DerivativeValidateOrderCubit>().onValidateVolume(
      currentVolume,
    );
    final errorPrice =
        context.read<DerivativeValidateOrderCubit>().state.errorPrice;
    final errorVolume =
        context.read<DerivativeValidateOrderCubit>().state.errorVolume;
    if (errorPrice == ErrorPrice.empty) {
      showMessageError("Chưa nhập giá!");
      return;
    }
    if (errorVolume == ErrorVolume.empty) {
      showMessageError("Chưa nhập khối lượng!");
      return;
    }
    if (errorVolume.isError || errorPrice.isError) return;

    var conditionInfo = ConditionInfo(
      symbol: symbol,
      qty: currentVolume.volume.toInt(),
      side: side,
      type: OrderTradingType.limit.nameServer,
      price: price,
      timetype: TimeType.T.toServer,
      fromDate: DateTime.now().formatToDdMmYyyy(),
      toDate: DateTime.now().formatToDdMmYyyy(),
    );

    var request = ConditionOrderRequestModel(
      market: 'derivatives',
      accountId: subAccountId,
      orderType: orderType.toServer,
      conditionInfo: conditionInfo,
    );

    switch (orderType) {
      case DerivativeOrderOptionalType.stopOrder:
        final currentActivationPrice =
            context
                .read<DerivativeValidateOrderCubit>()
                .state
                .currentActivationPrice ??
            '0';
        final activePrice = currentActivationPrice.priceDerivative ?? 0;
        final activeType =
            context.read<DerivativeValidateOrderCubit>().state.activationType;
        if (activeType.isGreaterThan &&
            (price as num) < activePrice &&
            action == OrderAction.buy) {
          showErrorMessage(
            VPTradingLocalize
                .current
                .derivative_active_price_must_be_less_than_price,
          );
          return;
        } else if (activeType.isLessThan &&
            (price as num) > activePrice &&
            action == OrderAction.sell) {
          showErrorMessage(
            VPTradingLocalize
                .current
                .derivative_active_price_must_be_greater_than_price,
          );
          return;
        }
        request = request.copyWith(
          conditionInfo: conditionInfo.copyWith(
            activePrice: activePrice,
            activeType: activeType.toParamRequest(),
          ),
        );
        break;
      case DerivativeOrderOptionalType.trailingStop:
        break;
      case DerivativeOrderOptionalType.stopLossOrTakeProfit:
        break;
      case DerivativeOrderOptionalType.regular:
        break;
    }

    if (DerivativeAppConfigs().userConfig.isShowNotifyConfirmOrder) {
      await showDialog(
        context: context,
        builder:
            (_) => PlaceDerivativeConditionOrderConfirmDialog(
              requestModel: request,
              orderType: orderType,
              onConfirm: (showConfirmOrder) {
                DerivativeAppConfigs().userConfig.setNotifyConfirmOrder(
                  showConfirmOrder,
                );
                updateUserConfig();
                context.read<PlaceOrderSubmitCubit>().submitPlaceConditionOrder(
                  request: request,
                  isDerivative: true,
                );
              },
            ),
      );
    } else {
      context.read<PlaceOrderSubmitCubit>().submitPlaceConditionOrder(
        request: request,
        isDerivative: true,
      );
    }
  }
}
