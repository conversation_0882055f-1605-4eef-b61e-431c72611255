import 'package:flutter/widgets.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/cubit/derivative/derivative_order/derivative_order_cubit.dart';
import 'package:vp_trading/model/positions/order_optional_type_model.dart';
import 'package:vp_trading/screen/place_order/derivative/input_view/derivative_text_input_field.dart';
import 'package:vp_trading/screen/place_order/derivative/input_view/validator/derivative_validator_field_widget.dart';
import 'package:vp_trading/screen/place_order/derivative/order_suggest/derivative_order_suggest_widget.dart';
import 'package:vp_trading/screen/place_order/derivative/stop_order/stop_order_widget.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/text_input_field.dart';

final priceKey = GlobalKey<TextInputFieldState>();

class DerivativePriceVolumeWidget extends StatefulWidget {
  const DerivativePriceVolumeWidget({super.key});

  @override
  State<DerivativePriceVolumeWidget> createState() =>
      _DerivativePriceVolumeWidgetState();
}

class _DerivativePriceVolumeWidgetState
    extends State<DerivativePriceVolumeWidget> {
  final _priceController = TextEditingController();
  final _priceActivationController = TextEditingController();
  final _volumeController = TextEditingController();

  @override
  dispose() {
    _priceController.dispose();
    _priceActivationController.dispose();
    _volumeController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocSelector<
      DerivativeOrderCubit,
      DerivativeOrderState,
      OrderOptionalTypeModel
    >(
      selector: (state) => state.orderOptionalCurrentType,
      builder: (_, orderOptionalCurrentType) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DerivativeTextInputField(
              key: priceKey,
              priceController: _priceController,
              volumeController: _volumeController,
            ),
            const SizedBox(height: 8),
            BlocSelector<
              DerivativeOrderCubit,
              DerivativeOrderState,
              DerivativeOrderOptionalType
            >(
              selector: (state) => state.orderOptionalCurrentType.type,
              builder: (_, orderType) {
                if (!orderType.isRegular) return const SizedBox.shrink();
                return const DerivativeOrderSuggest();
              },
            ),
            DerivativeValidatorField(
              priceController: _priceController,
              volumeController: _volumeController,
            ),
            const SizedBox(height: 12),
            if (orderOptionalCurrentType.type.isStopOrder)
              StopOrderWidget(priceController: _priceActivationController),
          ],
        );
      },
    );
  }
}
