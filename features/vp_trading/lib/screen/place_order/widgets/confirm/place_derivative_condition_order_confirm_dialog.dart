import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/screen/place_order/widgets/confirm/awaiting_order_confirm_dialog.dart';
import 'package:vp_trading/screen/place_order/widgets/confirm/derivative_condition_order_confirm_dialog.dart';

class PlaceDerivativeConditionOrderConfirmDialog extends StatelessWidget {
  final ValueChanged<bool> onConfirm;
  final ConditionOrderRequestModel requestModel;
  final DerivativeOrderOptionalType orderType;

  const PlaceDerivativeConditionOrderConfirmDialog({
    super.key,
    required this.onConfirm,
    required this.requestModel,
    required this.orderType,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: themeData.bgPopup,
      insetPadding: const EdgeInsets.symmetric(horizontal: 12),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 24,
        ).copyWith(top: 42),
        child: DerivativeConditionOrderConfirmDialog(
          requestModel: requestModel,
          onConfirm: onConfirm,
          orderType: orderType,
        ),
      ),
    );
  }
}
