import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/generated/l10n.dart';

class OrderPopupNote extends StatelessWidget {
  const OrderPopupNote({super.key});

  Color getNoteBackground() {
    if (isDark) {
      return themeData.yellowLight.withOpacity(0.32);
    } else {
      return themeData.yellowLight.withOpacity(0.16);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: getNoteBackground(),
          ),
          child: Text(
            VPTradingLocalize.current.trading_conditional_order_popup_note,
            style: vpTextStyle.captionMedium.copyColor(themeData.gray700),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 8),
      ],
    );
  }
}
