import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/generated/assets.gen.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/enum/activation_conditions_type.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/screen/place_order/widgets/confirm/widget/order_popup_note_widget.dart';
import 'package:vp_trading/screen/place_order/widgets/confirm/widget/text_with_multi_style.dart';

class DerivativeConditionOrderConfirmDialog extends StatelessWidget {
  final DerivativeOrderOptionalType orderType;

  final Function(bool showConfirmOrder) onConfirm;

  final ConditionOrderRequestModel requestModel;

  const DerivativeConditionOrderConfirmDialog({
    super.key,
    required this.requestModel,
    required this.onConfirm,
    required this.orderType,
  });

  @override
  Widget build(BuildContext context) {
    bool isBuyOrder = requestModel.conditionInfo.side == OrderAction.buy.value;

    Color orderColor = isBuyOrder ? themeData.primary : themeData.red;

    String orderLabel =
        isBuyOrder
            ? OrderAction.buy.nameTypeDerivative
            : OrderAction.sell.nameTypeDerivative;

    String symbol = requestModel.conditionInfo.symbol;

    String title = VPTradingLocalize.current.derivative_order_accept;
    String price = requestModel.conditionInfo.price.toString();
    String activePrice = requestModel.conditionInfo.activePrice.toString();
    String activeType = requestModel.conditionInfo.activeType ?? '';
    String activeTypeSymbol =
        activeType == ActivationConditionsType.greaterThan.toParamRequest()
            ? ActivationConditionsType.greaterThan.getSymbol()
            : ActivationConditionsType.lessThan.getSymbol();

    String buttonLabel =
        '${VPCommonLocalize.current.confirm} ${orderLabel.toLowerCase()}';

    bool showConfirmOrder = true;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SvgPicture.asset(
          VpTradingAssets.icons.icOrder.path,
          package: 'vp_trading',
          colorFilter: ColorFilter.mode(orderColor, BlendMode.srcIn),
        ),

        const SizedBox(height: 32),
        Text(
          title,
          style: vpTextStyle.headineBold6.copyColor(themeData.displayLarge),
        ),
        const SizedBox(height: 16),
        OrderConfirmRowTitle(
          title: VPTradingLocalize.current.derivative_order_type,
          content: orderLabel,
        ),
        const SizedBox(height: 8),
        OrderConfirmRowTitle(
          title: VPTradingLocalize.current.derivative_contract_code,
          content: symbol,
        ),
        const SizedBox(height: 8),
        OrderConfirmRowTitle(
          title: VPTradingLocalize.current.derivative_activation_conditions,
          content: orderType.toName,
        ),
        const SizedBox(height: 8),
        OrderConfirmRowTitle(
          title: VPTradingLocalize.current.trading_volume,
          content: requestModel.conditionInfo.qty.volumeString,
        ),
        const SizedBox(height: 8),
        OrderConfirmRowTitle(
          title: VPTradingLocalize.current.trading_order_price,
          content: price,
        ),
        const SizedBox(height: 8),
        OrderConfirmRowTitle(
          title: VPTradingLocalize.current.derivative_active_price,
          content: "$activeTypeSymbol $activePrice",
        ),
        const SizedBox(height: 8),
        const OrderPopupNote(),
        const SizedBox(height: 16),
        TextWithMultiStyle(
          content:
              "Bằng việc %&Xác nhận lệnh%&, bạn hiểu và chấp thuận với các lệnh giao dịch  được phát sinh khi thỏa mãn các điều kiện kích hoạt.",
          textStyle: vpTextStyle.captionMedium.copyColor(themeData.gray500),
          highLightStyle: vpTextStyle.captionMedium.copyColor(themeData.black),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: VpsButton.secondarySmall(
                title: VPCommonLocalize.current.close,
                onPressed: () => Navigator.pop(context),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child:
                  isBuyOrder
                      ? VpsButton.primarySmall(
                        title: buttonLabel,
                        onPressed: () {
                          Navigator.pop(context);
                          onConfirm(showConfirmOrder);
                        },
                      )
                      : VpsButton.primaryDangerSmall(
                        title: buttonLabel,
                        onPressed: () {
                          Navigator.pop(context);
                          onConfirm(showConfirmOrder);
                        },
                      ),
            ),
          ],
        ),
      ],
    );
  }
}

class OrderConfirmRowTitle extends StatelessWidget {
  final String title;
  final String content;

  const OrderConfirmRowTitle({
    super.key,
    required this.title,
    required this.content,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(title, style: vpTextStyle.body14.copyColor(themeData.gray700)),
        Text(
          content,
          style: vpTextStyle.body14.copyColor(themeData.displayLarge),
          textAlign: TextAlign.end,
        ),
      ],
    );
  }
}
