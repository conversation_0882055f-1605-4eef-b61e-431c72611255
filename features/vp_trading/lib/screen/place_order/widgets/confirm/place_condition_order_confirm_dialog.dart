import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/model/enum/take_profit_trigger_condition_enum.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/screen/place_order/widgets/confirm/awaiting_order_confirm_dialog.dart';
import 'package:vp_trading/screen/place_order/widgets/confirm/condition_order_confirm_dialog.dart';

class PlaceConditionOrderConfirmDialog extends StatelessWidget {
  final ValueChanged<bool> onConfirm;
  final ConditionOrderRequestModel requestModel;
  final String? value;
  final String price;
  final String subAccountString;
  final OrderType orderType;
  final TakeProfitTriggerConditionEnum triggerType;

  const PlaceConditionOrderConfirmDialog({
    super.key,
    required this.onConfirm,
    required this.requestModel,
    required this.price,
    required this.orderType,
    required this.triggerType,
    required this.subAccountString,
    this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: themeData.bgPopup,
      insetPadding: const EdgeInsets.symmetric(horizontal: 12),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 24,
        ).copyWith(top: 42),
        child:
            orderType.isWaiting
                ? AwaitingOrderConfirmDialog(
                  requestModel: requestModel,
                  onConfirm: onConfirm,
                  value: value ?? '',
                  orderTypeName: orderType.toName,
                  price: price.toUpperCase(),
                )
                : ConditionOrderConfirmDialog(
                  requestModel: requestModel,
                  subAccountString: subAccountString,
                  onConfirm: onConfirm,
                  triggerType: triggerType,
                  value: value ?? '',
                  orderType: orderType,
                  price: price.toUpperCase(),
                ),
      ),
    );
  }
}
