import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/stock_info/stock_info_cubit.dart';
import 'package:vp_trading/screen/order_container/place_order_container.dart';
import 'package:vp_trading/screen/place_order/awaiting/awaiting_order_view.dart';
import 'package:vp_trading/screen/place_order/gtc/gtc_effective_time_button.dart';
import 'package:vp_trading/screen/place_order/take_profit_stop_loss/take_profit_stop_loss_order_view.dart';
import 'package:vp_trading/screen/place_order/widgets/available_trade/available_trade_label.dart';
import 'package:vp_trading/screen/place_order/widgets/available_trade/condition_available_trade_label.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/price_volume_widget.dart';
import 'package:vp_trading/screen/place_order/widgets/order_type/order_type_widget.dart';
import 'package:vp_trading/screen/place_order/widgets/price_box_view/place_order_info_view.dart';
import 'package:vp_trading/screen/place_order/widgets/save_command/save_command_widget.dart';
import 'package:vp_trading/screen/place_order/widgets/top3/place_order_top3_view.dart';
import 'package:vp_trading/widgets/place_order_status_view.dart';

class PlaceOrderView extends StatefulWidget {
  const PlaceOrderView({super.key});

  @override
  State<PlaceOrderView> createState() => _PlaceOrderViewState();
}

class _PlaceOrderViewState extends State<PlaceOrderView> {
  final ScrollController scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<StockInfoCubit, StockInfoState>(
      builder: (context, state) {
        return PlaceOrderStatusView(
          apiStatus: state.status,
          builder: (context, child) {
            return CustomScrollView(
              slivers: [
                const SliverToBoxAdapter(child: SizedBox(height: 12)),

                SliverPersistentHeader(
                  pinned: true,
                  delegate: MySliverPersistentHeader(
                    minExtent: 70,
                    maxExtent: 160,
                  ),
                ),

                const SliverToBoxAdapter(child: SizedBox(height: 8)),

                const SliverToBoxAdapter(child: PlaceOrderTop3View()),

                BlocSelector<PlaceOrderCubit, PlaceOrderState, OrderType?>(
                  selector: (state) => state.orderType,
                  builder: (_, orderType) {
                    return SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: ColoredBox(
                          color: vpColor.backgroundElevation0,
                          child: Column(
                            children: [
                              const SizedBox(height: 16),
                              const OrderTypeWidget(),
                              const SizedBox(height: 12),
                              if (orderType == OrderType.stopLoss ||
                                  orderType == OrderType.takeProfit ||
                                  orderType == OrderType.waiting) ...[
                                BlocSelector<
                                  PlaceOrderCubit,
                                  PlaceOrderState,
                                  OrderAction?
                                >(
                                  selector: (state) => state.action,
                                  builder: (_, action) {
                                    if (action == OrderAction.buy) {
                                      return const SizedBox.shrink();
                                    }
                                    return const ConditionAvailableTradeLabel();
                                  },
                                ),
                              ],
                              if (orderType == OrderType.lo) ...[
                                const Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 16),
                                  child: AvailableTradeLabel(),
                                ),
                              ],

                              if (orderType == OrderType.lo ||
                                  orderType == OrderType.gtc) ...[
                                const SizedBox(height: 12),
                                const PriceVolumeWidget(),
                                const SizedBox(height: 12),
                                const GtcEffectiveTimeButton(),
                              ],

                              if (orderType == OrderType.waiting) ...[
                                const SizedBox(height: 12),
                                const AwaitingOrderView(),
                              ],

                              if (orderType == OrderType.takeProfit ||
                                  orderType == OrderType.stopLoss) ...[
                                const SizedBox(height: 12),
                                const TakeProfitStopLossOrderView(),
                              ],

                              if (orderType == OrderType.lo) ...[
                                const SizedBox(height: 12),
                                const Padding(
                                  padding: EdgeInsets.only(left: 16.0),
                                  child: SaveCommandWidget(),
                                ),
                              ],
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
                BlocBuilder<PlaceOrderCubit, PlaceOrderState>(
                  buildWhen:
                      (previous, current) =>
                          previous.symbol != current.symbol ||
                          previous.subAccountType != current.subAccountType,
                  builder: (context, state) {
                    return SliverToBoxAdapter(
                      child: PlaceOrderContainer(
                        key: ValueKey(state.symbol + state.subAccountType.name),
                        subAccountType: state.subAccountType,
                      ),
                    );
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }
}
