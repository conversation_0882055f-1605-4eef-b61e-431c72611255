import 'package:flutter/widgets.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/text_input_field.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/validator/validator_field_widget.dart';
import 'package:vp_trading/screen/place_order/widgets/order_suggest/order_suggest_widget.dart';

class PriceVolumeWidget extends StatefulWidget {
  const PriceVolumeWidget({super.key});

  @override
  State<PriceVolumeWidget> createState() => _PriceVolumeWidgetState();
}

class _PriceVolumeWidgetState extends State<PriceVolumeWidget> {
  final _priceController = TextEditingController();
  final _volumeController = TextEditingController();
  final _priceKey = GlobalKey<TextInputFieldState>();

  @override
  dispose() {
    _priceController.dispose();
    _volumeController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextInputField(
          key: _priceKey,
          priceController: _priceController,
          volumeController: _volumeController,
        ),
        const SizedBox(height: 8),
        BlocSelector<PlaceOrderCubit, PlaceOrderState, OrderType?>(
          selector: (state) => state.orderType,
          builder: (_, orderType) {
            if (orderType == OrderType.gtc) return const SizedBox.shrink();
            return const OrderSuggest();
          },
        ),
        ValidatorField(
          priceController: _priceController,
          volumeController: _volumeController,
        ),
        //   SizedBox(height: 8),
      ],
    );
  }
}
