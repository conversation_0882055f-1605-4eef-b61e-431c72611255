import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';

class ChoiceDilutionActionWidget extends StatelessWidget {
  const ChoiceDilutionActionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 8),
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              VPTradingLocalize.current.trading_condition_dilution_choice,
              style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
            ),
          ),
        ],
      ),
    );
  }
}
