import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/screen/order_container/enum/condition_order_type_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';

void openFilterConditionOrderBottomSheet(
  BuildContext context, {
  OrderTypeEnum? initialOrderType,
  String? initialSymbol,
  List<ConditionOrderTypeEnum>? initialConditionOrderTypes,
  DateTimeRange? dateTimeOrderTime,
  DateTimeRange? dateTimeExpirationDate,
  Function(
    OrderTypeEnum?,
    String?,
    DateTimeRange?,
    DateTimeRange?,
    List<ConditionOrderTypeEnum>?,
  )?
  onApply,
}) {
  VPPopup.bottomSheet(
    _FilterOrderContainer(
      initialOrderType: initialOrderType,
      initialSymbol: initialSymbol,
      dateTimeOrderTime: dateTimeOrderTime,
      dateTimeExpirationDate: dateTimeExpirationDate,
      initialConditionOrderTypes: initialConditionOrderTypes,
      onApply: onApply,
    ),
  ).showSheet(context);
}

class _FilterOrderContainer extends StatefulWidget {
  const _FilterOrderContainer({
    super.key,
    this.initialOrderType,
    this.initialSymbol,
    this.onApply,
    this.dateTimeOrderTime,
    this.dateTimeExpirationDate,
    this.initialConditionOrderTypes,
  });

  final OrderTypeEnum? initialOrderType;
  final String? initialSymbol;
  final List<ConditionOrderTypeEnum>? initialConditionOrderTypes;
  final DateTimeRange? dateTimeOrderTime;
  final DateTimeRange? dateTimeExpirationDate;

  final Function(
    OrderTypeEnum?,
    String?,
    DateTimeRange?,
    DateTimeRange?,
    List<ConditionOrderTypeEnum>?,
  )?
  onApply;

  @override
  State<_FilterOrderContainer> createState() => _FilterOrderContainerState();
}

class _FilterOrderContainerState extends State<_FilterOrderContainer> {
  final TextEditingController controller = TextEditingController();
  late OrderTypeEnum _selectedOrderType;
  late List<ConditionOrderTypeEnum> _selectedConditionOrderTypes;
  DateTimeRange? _dateTimeOrderTime;
  DateTimeRange? _dateTimeExpirationDate;

  // Add reset key to force rebuild widgets
  int _resetKey = 0;

  @override
  void initState() {
    super.initState();

    if (widget.dateTimeOrderTime != null) {
      _dateTimeOrderTime = widget.dateTimeOrderTime;
    }
    if (widget.dateTimeExpirationDate != null) {
      _dateTimeExpirationDate = widget.dateTimeExpirationDate;
    }
    _selectedOrderType = widget.initialOrderType ?? OrderTypeEnum.all;

    // Initialize condition order types with multiple selection logic
    if (widget.initialConditionOrderTypes == null ||
        widget.initialConditionOrderTypes!.contains(
          ConditionOrderTypeEnum.all,
        )) {
      _selectedConditionOrderTypes = [ConditionOrderTypeEnum.all];
    } else {
      _selectedConditionOrderTypes = List<ConditionOrderTypeEnum>.from(
        widget.initialConditionOrderTypes!,
      );
    }

    controller.text = widget.initialSymbol ?? '';
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  void _onSelectOrderType(OrderTypeEnum orderType) {
    setState(() {
      _selectedOrderType = orderType;
    });
  }

  // Helper getter to check if all condition order types are selected
  bool get isSelectAllConditionOrderTypes =>
      _selectedConditionOrderTypes.contains(ConditionOrderTypeEnum.all) ||
      _selectedConditionOrderTypes.length ==
          ConditionOrderTypeEnum.values.length;

  void _onTapAllConditionOrderTypes() {
    setState(() {
      _selectedConditionOrderTypes = [ConditionOrderTypeEnum.all];
    });
  }

  void _onSelectConditionOrderType(ConditionOrderTypeEnum conditionOrderType) {
    setState(() {
      if (_selectedConditionOrderTypes.contains(ConditionOrderTypeEnum.all)) {
        // If "all" is selected, remove "all" and add all other types except the clicked one
        _selectedConditionOrderTypes =
            ConditionOrderTypeEnum.values
                .where(
                  (e) =>
                      e != ConditionOrderTypeEnum.all &&
                      e != conditionOrderType,
                )
                .toList();
      } else {
        if (_selectedConditionOrderTypes.contains(conditionOrderType)) {
          // Prevent unchecking the last item if it's not "all"
          if (_selectedConditionOrderTypes.length == 1 &&
              !_selectedConditionOrderTypes.contains(
                ConditionOrderTypeEnum.all,
              )) {
            return; // Don't allow unchecking the last item
          }
          _selectedConditionOrderTypes.remove(conditionOrderType);
        } else {
          _selectedConditionOrderTypes.add(conditionOrderType);
        }
      }
      // If nothing is selected, default to 'all'
      if (_selectedConditionOrderTypes.isEmpty) {
        _selectedConditionOrderTypes = [ConditionOrderTypeEnum.all];
      }
      // If all types are selected (excluding 'all'), switch to 'all'
      if (_selectedConditionOrderTypes.length ==
          ConditionOrderTypeEnum.values.length - 1) {
        _selectedConditionOrderTypes = [ConditionOrderTypeEnum.all];
      }
    });
  }

  void _onReset() {
    setState(() {
      _selectedOrderType = OrderTypeEnum.all;
      _selectedConditionOrderTypes = [ConditionOrderTypeEnum.all];
      _dateTimeOrderTime = null;
      _dateTimeExpirationDate = null;
      controller.clear();
      // Increment key to force rebuild
      _resetKey++;
    });
  }

  void _onApply() {
    final symbol = controller.text.trim();

    // Return the selected condition order types
    List<ConditionOrderTypeEnum>? conditionOrderTypesToReturn;
    if (_selectedConditionOrderTypes.contains(ConditionOrderTypeEnum.all)) {
      conditionOrderTypesToReturn = [ConditionOrderTypeEnum.all];
    } else {
      conditionOrderTypesToReturn = List<ConditionOrderTypeEnum>.from(
        _selectedConditionOrderTypes,
      );
    }

    widget.onApply?.call(
      _selectedOrderType,
      symbol.isEmpty ? null : symbol,
      _dateTimeOrderTime,
      _dateTimeExpirationDate,
      conditionOrderTypesToReturn,
    );
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        FocusManager.instance.primaryFocus?.unfocus();
        FocusScope.of(context).unfocus();
      },
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            VPTextFieldWithClear.small(
              onChanged: (e) {},
              controller: controller,
              hintText: VPTradingLocalize.current.trading_search_by_stock_code,
              prefixIcon:
                  (_) => IconButton(
                    onPressed: () {},
                    icon: DesignAssets.icons.icSearch.svg(
                      colorFilter: ColorFilter.mode(
                        vpColor.iconPrimary,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
            ),
            const SizedBox(height: 24),
            Text(
              VPTradingLocalize.current.trading_transaction_type,
              style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 16,
              children: [
                ...OrderTypeEnum.values.map(
                  (e) => VPChipView.dynamic(
                    text: e.title,
                    size: ChipSize.medium,
                    onTap: () => _onSelectOrderType(e),
                    style:
                        _selectedOrderType == e
                            ? ChipStyle.selected
                            : ChipStyle.chipDefault,
                  ),
                ),
              ],
            ),
            if (widget.initialConditionOrderTypes != null) ...[
              const SizedBox(height: 24),
              Text(
                "Loại lệnh điều kiện",
                style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 16,
                children: [
                  VPChipView.dynamic(
                    text: ConditionOrderTypeEnum.all.title,
                    size: ChipSize.medium,
                    onTap: _onTapAllConditionOrderTypes,
                    style:
                        isSelectAllConditionOrderTypes
                            ? ChipStyle.selected
                            : ChipStyle.chipDefault,
                  ),
                  ...ConditionOrderTypeEnum.values
                      .where((e) => e != ConditionOrderTypeEnum.all)
                      .map(
                        (e) => VPChipView.dynamic(
                          text: e.title,
                          size: ChipSize.medium,
                          onTap: () => _onSelectConditionOrderType(e),
                          style:
                              isSelectAllConditionOrderTypes
                                  ? ChipStyle.selected
                                  : _selectedConditionOrderTypes.contains(e)
                                  ? ChipStyle.selected
                                  : ChipStyle.chipDefault,
                        ),
                      ),
                ],
              ),
            ],
            buildTimeCondition(),
            const SizedBox(height: 24),

            Row(
              children: [
                Expanded(
                  child: VpsButton.secondaryXsSmall(
                    title: VPTradingLocalize.current.trading_reset,
                    onPressed: _onReset,
                    alignment: Alignment.center,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: VpsButton.primaryXsSmall(
                    title: VPTradingLocalize.current.trading_apply,
                    onPressed: _onApply,
                    alignment: Alignment.center,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateTimePicker({
    required String label,
    required DateTimeRange? dateTimeRange,
    required Function(DateTimeRange) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
        ),
        const SizedBox(height: 8),
        VPDateTimeHolderCommon(
          key: ValueKey('${label}_$_resetKey'), // Force rebuild with key
          startDate: dateTimeRange?.start ?? DateTime.now(),
          endDate: dateTimeRange?.end ?? DateTime.now(),
          onDateTimeChanged: (data) {
            onChanged(DateTimeRange(start: data.startDate, end: data.endDate));
          },
        ),
      ],
    );
  }

  buildTimeCondition() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),
        _buildDateTimePicker(
          label: "Thời gian đặt lệnh",
          dateTimeRange: _dateTimeOrderTime,
          onChanged: (range) {
            setState(() {
              _dateTimeOrderTime = range;
            });
          },
        ),
        const SizedBox(height: 24),
        _buildDateTimePicker(
          label: "Ngày hết hiệu lực",
          dateTimeRange: _dateTimeExpirationDate,
          onChanged: (range) {
            setState(() {
              _dateTimeExpirationDate = range;
            });
          },
        ),
      ],
    );
  }
}
