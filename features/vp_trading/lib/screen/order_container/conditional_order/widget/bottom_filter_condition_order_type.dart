import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/screen/order_container/enum/condition_order_type_enum.dart';

void openFilterConditionOrderTypeBottomSheet({
  required BuildContext context,
  List<ConditionOrderTypeEnum>? listConditionOrderTypes,
  Function(List<ConditionOrderTypeEnum>)? onApply,
}) {
  VPPopup.bottomSheet(
    _FilterConditionOrderType(
      listConditionOrderTypes: listConditionOrderTypes,
      onApply: onApply,
    ),
  ).showSheet(context);
}

class _FilterConditionOrderType extends StatefulWidget {
  const _FilterConditionOrderType({
    this.onApply,
    this.listConditionOrderTypes,
  });
  
  final Function(List<ConditionOrderTypeEnum>)? onApply;
  final List<ConditionOrderTypeEnum>? listConditionOrderTypes;

  @override
  State<_FilterConditionOrderType> createState() => _FilterConditionOrderTypeState();
}

class _FilterConditionOrderTypeState extends State<_FilterConditionOrderType> {
  late List<ConditionOrderTypeEnum> _selectedConditionOrderTypes;

  @override
  void initState() {
    super.initState();
    // If null or contains 'all', select all by default
    if (widget.listConditionOrderTypes == null ||
        widget.listConditionOrderTypes!.contains(ConditionOrderTypeEnum.all)) {
      _selectedConditionOrderTypes = [ConditionOrderTypeEnum.all];
    } else {
      _selectedConditionOrderTypes = List<ConditionOrderTypeEnum>.from(
        widget.listConditionOrderTypes!,
      );
    }
  }

  bool get isSelectAll =>
      _selectedConditionOrderTypes.contains(ConditionOrderTypeEnum.all) ||
      _selectedConditionOrderTypes.length == ConditionOrderTypeEnum.values.length;

  void _onTapAll() {
    setState(() {
      _selectedConditionOrderTypes = [ConditionOrderTypeEnum.all];
    });
  }

  void _onTapConditionOrderType(ConditionOrderTypeEnum conditionOrderType) {
    setState(() {
      if (_selectedConditionOrderTypes.contains(ConditionOrderTypeEnum.all)) {
        // If "all" is selected, remove "all" and add all other types except the clicked one
        _selectedConditionOrderTypes = ConditionOrderTypeEnum.values
            .where((e) => e != ConditionOrderTypeEnum.all && e != conditionOrderType)
            .toList();
      } else {
        if (_selectedConditionOrderTypes.contains(conditionOrderType)) {
          // Prevent unchecking the last item if it's not "all"
          if (_selectedConditionOrderTypes.length == 1 &&
              !_selectedConditionOrderTypes.contains(ConditionOrderTypeEnum.all)) {
            return; // Don't allow unchecking the last item
          }
          _selectedConditionOrderTypes.remove(conditionOrderType);
        } else {
          _selectedConditionOrderTypes.add(conditionOrderType);
        }
      }
      // If nothing is selected, default to 'all'
      if (_selectedConditionOrderTypes.isEmpty) {
        _selectedConditionOrderTypes = [ConditionOrderTypeEnum.all];
      }
      // If all types are selected (excluding 'all'), switch to 'all'
      if (_selectedConditionOrderTypes.length == ConditionOrderTypeEnum.values.length - 1) {
        _selectedConditionOrderTypes = [ConditionOrderTypeEnum.all];
      }
    });
  }

  void _onReset() {
    setState(() {
      _selectedConditionOrderTypes = [ConditionOrderTypeEnum.all];
    });
  }

  void _onApply() {
    if (widget.onApply != null) {
      if (_selectedConditionOrderTypes.contains(ConditionOrderTypeEnum.all)) {
        widget.onApply!([ConditionOrderTypeEnum.all]);
      } else {
        widget.onApply!(List<ConditionOrderTypeEnum>.from(_selectedConditionOrderTypes));
      }
    }
    context.pop();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(
          "Loại lệnh điều kiện",
          style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 16,
          children: [
            VPChipView.dynamic(
              text: ConditionOrderTypeEnum.all.title,
              size: ChipSize.medium,
              onTap: _onTapAll,
              style: isSelectAll ? ChipStyle.selected : ChipStyle.chipDefault,
            ),
            ...ConditionOrderTypeEnum.values
                .where((e) => e != ConditionOrderTypeEnum.all)
                .map(
                  (e) => VPChipView.dynamic(
                    text: e.title,
                    size: ChipSize.medium,
                    onTap: () => _onTapConditionOrderType(e),
                    style: isSelectAll
                        ? ChipStyle.selected
                        : _selectedConditionOrderTypes.contains(e)
                            ? ChipStyle.selected
                            : ChipStyle.chipDefault,
                  ),
                ),
          ],
        ),
        const SizedBox(height: 24),
        Row(
          children: [
            Expanded(
              child: VpsButton.secondaryXsSmall(
                title: VPTradingLocalize.current.trading_reset,
                onPressed: _onReset,
                alignment: Alignment.center,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: VpsButton.primaryXsSmall(
                title: VPTradingLocalize.current.trading_apply,
                onPressed: _onApply,
                alignment: Alignment.center,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
