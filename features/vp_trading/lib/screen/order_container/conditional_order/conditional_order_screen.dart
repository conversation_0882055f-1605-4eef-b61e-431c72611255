import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/app_refresh_view.dart';
import 'package:vp_design_system/custom_widget/no_data_view.dart';
import 'package:vp_design_system/gen/assets.gen.dart';
import 'package:vp_design_system/widget/dropdown/vp_dropdown_view.dart';
import 'package:vp_design_system/widget/snackbar/vp_snackbar.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_stock_common/screens/accoun_list/bottom_sheet_account_list/money_sub_account_bottom_sheet.dart';
import 'package:vp_trading/cubit/order_container/condition_order/condition_order_cubit.dart';
import 'package:vp_trading/cubit/order_container/delete_update_order/delete_update_order_cubit.dart';
import 'package:vp_trading/cubit/order_container/delete_update_order/delete_update_order_state.dart';
import 'package:vp_trading/cubit/order_container/order_container/order_container_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/request/order/filter_nomal_param.dart';
import 'package:vp_trading/screen/order_container/conditional_order/widget/bottom_filter_condition_order.dart';
import 'package:vp_trading/screen/order_container/conditional_order/widget/bottom_filter_status_condition_order.dart';
import 'package:vp_trading/screen/order_container/conditional_order/widget/condition_order_list.dart';
import 'package:vp_trading/screen/order_container/enum/condition_order_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/condition_order_type_enum.dart';
import 'package:vp_trading/screen/order_container/enum/enum_tabbar_order.dart';
import 'package:vp_trading/screen/order_container/normal_order/widget/normal_order_title_widget.dart';
import 'package:vp_trading/widgets/command_history_loading_widget.dart';

class ConditionalOrderScreen extends StatefulWidget {
  const ConditionalOrderScreen({super.key});

  @override
  State<ConditionalOrderScreen> createState() => _ConditionalOrderScreenState();
}

class _ConditionalOrderScreenState extends State<ConditionalOrderScreen> {
  FilterNormalParam? _lastAppliedFilter;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => ConditionOrderCubit()..init()),
        BlocProvider(create: (context) => DeleteUpdateOrderCubit()),
      ],

      child: BlocBuilder<ConditionOrderCubit, ConditionOrderState>(
        builder: (context, state) {
          return BlocListener<DeleteUpdateOrderCubit, DeleteUpdateOrderState>(
            listener: (context, state) {
              if (state.status == DeleteUpdateOrderStateEnum.isDeleteSuccess) {
                context.showSnackBar(
                  content:
                      VPTradingLocalize.current.trading_cancel_order_success,
                  snackBarType: VPSnackBarType.success,
                );

                context.read<ConditionOrderCubit>().loadData();
              }
              if (state.status ==
                  DeleteUpdateOrderStateEnum.isDeleteAllSuccess) {
                context.showSuccess(
                  content:
                      VPTradingLocalize.current.trading_cancel_order_success,
                );
                context.read<ConditionOrderCubit>().loadData();
              }
              if (state.errorMessage != null) {
                context.showSnackBar(
                  content: state.errorMessage ?? "-",
                  snackBarType: VPSnackBarType.error,
                );
                context.read<DeleteUpdateOrderCubit>().resetErrorMessage();
              }
            },
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
              child: Column(
                children: [
                  _filtterDropdown(state, context),
                  const SizedBox(height: 16),
                  if (state.isLoading)
                    const Expanded(child: CommandHistoryLoadingWidget()),
                  if (!state.isLoading && state.listItems.isEmpty)
                    Expanded(
                      child: PullToRefreshView(
                        onRefresh: () async {
                          await context.read<ConditionOrderCubit>().loadData();
                        },
                        child: NoDataView(
                          content:
                              VPTradingLocalize.current.trading_no_data_message,
                        ),
                      ),
                    ),
                  if (state.listItems.isNotEmpty && !state.isLoading) ...[
                    const ConditionNormalTitle(
                      expandTitleWidget: [10, 6, 7, 12],
                      showTitleDeleteAll: false,
                      isShowEditCancel: false,
                    ),
                    const SizedBox(height: 16),
                    Expanded(
                      child: PullToRefreshView(
                        onRefresh: () async {
                          await context.read<ConditionOrderCubit>().loadData();
                        },
                        child: ConditionOrderList(
                          items: state.listItems,
                          hasMore: false,
                          loadMore: () async {},
                          refresh: () async {
                            await context
                                .read<ConditionOrderCubit>()
                                .loadData();
                          },
                          editSuccess: () async {
                            await context
                                .read<ConditionOrderCubit>()
                                .loadData();
                          },
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _filtterDropdown(ConditionOrderState state, BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: VPDropdownView.small(
            hint: VPTradingLocalize.current.trading_sub_account_hint,
            width: double.infinity,
            textStyle: context.textStyle.captionRegular?.copyWith(
              color: vpColor.textPrimary,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            value: state.filterParam?.subAccountModel?.accountType.displayName,
            onTap: () async {
              _showSubAccountBottomSheet(context);
            },
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          flex: 1,
          child: VPDropdownView.small(
            hint: VPTradingLocalize.current.trading_status_hint,
            width: double.infinity,
            overflow: TextOverflow.ellipsis,
            textStyle: context.textStyle.captionRegular?.copyWith(
              color: vpColor.textPrimary,
            ),
            maxLines: 1,
            value: ConditionOrderStatusEnumExt.displayNameFilter(
              state.filterParam?.conditionOrderStatus ?? [],
            ),
            onTap: () async => _showFilterStatusOrder(context),
          ),
        ),

        const SizedBox(width: 8),
        _buildFilter(),
      ],
    );
  }

  Widget _buildFilter() {
    return BlocListener<OrderContainerCubit, OrderContainerState>(
      listener: (context, state) async {
        if (state.currentTabbar == EnumTabbarOrder.conditional) {
          final newFilter = state.currentFilterParam;
          if (newFilter != null && newFilter != _lastAppliedFilter) {
            _lastAppliedFilter = newFilter;
            context.read<ConditionOrderCubit>().updateFilter(newFilter);
          }
        }
      },

      child: BlocBuilder<OrderContainerCubit, OrderContainerState>(
        builder: (context, state) {
          final hasFilter = state.hasFilter;
          return GestureDetector(
            onTap: () => _showFilterOrderContainer(context),
            child: DesignAssets.icons.icFilter.svg(
              colorFilter: ColorFilter.mode(
                hasFilter ? vpColor.textBrand : vpColor.iconPrimary,
                BlendMode.srcIn,
              ),
              height: 20,
            ),
          );
        },
      ),
    );
  }

  void _showFilterOrderContainer(BuildContext context) {
    final currentFilter =
        context.read<OrderContainerCubit>().state.currentFilterParam;

    openFilterConditionOrderBottomSheet(
      context,
      initialOrderType: currentFilter?.orderType,
      initialSymbol: currentFilter?.symbol,
      dateTimeOrderTime: currentFilter?.dateTimeOrderTime,
      dateTimeExpirationDate: currentFilter?.dateTimeExpirationDate,
      initialConditionOrderTypes:
          currentFilter?.conditionOrderTypes ?? [ConditionOrderTypeEnum.all],
      onApply: (
        orderTypeEnum,
        symbol,
        dateTimeOrderTime,
        dateTimeExpirationDate,
        conditionOrderTypesEnum,
      ) {
        final filterParam = FilterNormalParam(
          orderType: orderTypeEnum,
          symbol: symbol?.trim().toUpperCase(),
          conditionOrderTypes: conditionOrderTypesEnum,
          dateTimeOrderTime: dateTimeOrderTime,
          dateTimeExpirationDate: dateTimeExpirationDate,
        );

        context.read<OrderContainerCubit>().updateCurrentFilter(filterParam);
      },
    );
  }

  Future<void> _showSubAccountBottomSheet(BuildContext context) async {
    final listSubAccounts = GetIt.instance<SubAccountCubit>().subAccountsStock;
    listSubAccounts.insert(0, subAccountTypeAll);
    final result = await showDerivativeSubAccountBottomSheet(
      context: context,
      listSubAccounts: listSubAccounts,
    );
    if (result is! SubAccountModel || !context.mounted) return;
    context.read<ConditionOrderCubit>().updateFilterAccount(result);
  }

  void _showFilterStatusOrder(BuildContext context) {
    var orderStatus =
        context
            .read<ConditionOrderCubit>()
            .state
            .filterParam
            ?.conditionOrderStatus;
    openFilterStatusOrderConditionBottomSheet(
      context: context,
      listStatus: orderStatus,
      onApply: (p0) async {
        await context.read<ConditionOrderCubit>().updateFilterStatus(p0);
      },
    );
  }
}
