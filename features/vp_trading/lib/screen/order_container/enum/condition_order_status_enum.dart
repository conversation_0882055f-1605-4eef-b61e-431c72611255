import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';

enum ConditionOrderStatusEnum {
  all,
  inactivated,
  activated,
  canceled,
  expired,
  partialMatch,
  fullMatch,
  modified,
  rejected,
  overdue,
}

extension ConditionOrderStatusEnumExt on ConditionOrderStatusEnum {
  String get title {
    switch (this) {
      case ConditionOrderStatusEnum.all:
        return "Tất cả";
      case ConditionOrderStatusEnum.inactivated:
        return "Chờ kích hoạt";
      case ConditionOrderStatusEnum.activated:
        return "Đã kích hoạt";
      case ConditionOrderStatusEnum.canceled:
        return "Đã hủy";
      case ConditionOrderStatusEnum.expired:
        return "Hết hiệu lực";
      case ConditionOrderStatusEnum.partialMatch:
        return "Khớp 1 phần";
      case ConditionOrderStatusEnum.fullMatch:
        return "Khớp hết";
      case ConditionOrderStatusEnum.modified:
        return "Đã sửa";
      case ConditionOrderStatusEnum.rejected:
        return "Từ chối";
      case ConditionOrderStatusEnum.overdue:
        return "Hết hạn";
    }
  }

  Color get color {
    switch (this) {
      case ConditionOrderStatusEnum.all:
        return Colors.black54;
      case ConditionOrderStatusEnum.inactivated:
        return vpColor.backgroundAccentBlue;
      case ConditionOrderStatusEnum.activated:
        return vpColor.backgroundAccentGreen;
      case ConditionOrderStatusEnum.canceled:
        return vpColor.backgroundAccentRed;
      case ConditionOrderStatusEnum.expired:
        return vpColor.backgroundAccentRed;
      case ConditionOrderStatusEnum.partialMatch:
        return vpColor.backgroundAccentBlue;
      case ConditionOrderStatusEnum.fullMatch:
        return vpColor.backgroundAccentGreen;
      case ConditionOrderStatusEnum.modified:
        return vpColor.backgroundAccentRed;
      case ConditionOrderStatusEnum.rejected:
        return vpColor.backgroundAccentRed;
      case ConditionOrderStatusEnum.overdue:
        return vpColor.backgroundAccentRed;
    }
  }

  Color get textColor {
    switch (this) {
      case ConditionOrderStatusEnum.all:
        return Colors.black54;
      case ConditionOrderStatusEnum.inactivated:
        return vpColor.textAccentBlue;
      case ConditionOrderStatusEnum.activated:
        return vpColor.textAccentGreen;
      case ConditionOrderStatusEnum.canceled:
        return vpColor.textAccentRed;
      case ConditionOrderStatusEnum.expired:
        return vpColor.textAccentRed;
      case ConditionOrderStatusEnum.partialMatch:
        return vpColor.textAccentBlue;
      case ConditionOrderStatusEnum.fullMatch:
        return vpColor.textAccentGreen;
      case ConditionOrderStatusEnum.modified:
        return vpColor.textAccentRed;
      case ConditionOrderStatusEnum.rejected:
        return vpColor.textAccentRed;
      case ConditionOrderStatusEnum.overdue:
        return vpColor.textAccentRed;
    }
  }

  String get codeRequest {
    switch (this) {
      case ConditionOrderStatusEnum.all:
        return "ALL";
      case ConditionOrderStatusEnum.inactivated:
        return "INACTIVATED,PS,WT,WA,WC,WE,PR,PC,OP,WD,ST";
      case ConditionOrderStatusEnum.activated:
        return "ACTIVATED";
      case ConditionOrderStatusEnum.canceled:
        return "CANCELED,CN";
      case ConditionOrderStatusEnum.expired:
        return "EXPIRED,EP";
      case ConditionOrderStatusEnum.partialMatch:
        return "PF";
      case ConditionOrderStatusEnum.fullMatch:
        return "FF,CP";
      case ConditionOrderStatusEnum.modified:
        return "RP";
      case ConditionOrderStatusEnum.rejected:
        return "RJ";
      case ConditionOrderStatusEnum.overdue:
        return "EX";
    }
  }

  static ConditionOrderStatusEnum conditionOrderStatusFromString(String? code) {
    switch (code?.toUpperCase()) {
      case "INACTIVATED":
      case "PS":
      case "WT":
      case "WA":
      case "WC":
      case "WE":
      case "PR":
      case "PC":
      case "OP":
      case "WD":
      case "ST":
        return ConditionOrderStatusEnum.inactivated;
      case "ACTIVATED":
        return ConditionOrderStatusEnum.activated;
      case "CN":
      case "CANCELED":
        return ConditionOrderStatusEnum.canceled;
      case "EXPIRED":
      case "EP":
        return ConditionOrderStatusEnum.expired;
      case "PF":
        return ConditionOrderStatusEnum.partialMatch;
      case "FF":
      case "CP":
        return ConditionOrderStatusEnum.fullMatch;
      case "RP":
        return ConditionOrderStatusEnum.modified;
      case "RJ":
        return ConditionOrderStatusEnum.rejected;
      case "EX":
        return ConditionOrderStatusEnum.overdue;
      default:
        return ConditionOrderStatusEnum.expired; // fallback
    }
  }

  static String displayNameFilter(List<ConditionOrderStatusEnum> listStatus) {
    if (listStatus.contains(ConditionOrderStatusEnum.all)) {
      return VPTradingLocalize.current.trading_status_all;
    } else {
      if (listStatus.length == 1) {
        return listStatus.first.title;
      }
      return "Đã chọn ${listStatus.length}";
    }
  }
}
