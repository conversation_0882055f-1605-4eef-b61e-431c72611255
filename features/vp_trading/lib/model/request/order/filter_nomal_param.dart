import 'package:flutter/material.dart';
import 'package:vp_core/model/sign_in_model/sub_account_model.dart';
import 'package:vp_trading/screen/order_container/enum/condition_order_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/condition_order_type_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_time_filter_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';

class FilterNormalParam {
  final List<OrderStatusEnum>? orderStatus;
  final OrderTypeEnum? orderType;
  final String? symbol;
  final SubAccountModel? subAccountModel;
  final TradingTimeFilterEnum? timeFilter;
  final DateTimeRange? dateTimeRangeCustom;
  final List<ConditionOrderStatusEnum>? conditionOrderStatus;
  final List<ConditionOrderTypeEnum>?
  conditionOrderTypes; // Updated to support multiple selection
  // dành cho filter lệnh điều kiện
  final DateTimeRange? dateTimeOrderTime; //thời gian đặt lệnh
  final DateTimeRange? dateTimeExpirationDate; // Ngày hết hiệu lực

  FilterNormalParam({
    this.symbol,
    this.orderType,
    this.orderStatus,
    this.subAccountModel,
    this.timeFilter,
    this.dateTimeRangeCustom,
    this.conditionOrderStatus,
    this.conditionOrderTypes,
    this.dateTimeOrderTime,
    this.dateTimeExpirationDate,
  });
  FilterNormalParam copyWith({
    List<OrderStatusEnum>? orderStatus,
    OrderTypeEnum? orderType,
    String? symbol,
    SubAccountModel? subAccountModel,
    TradingTimeFilterEnum? timeFilter,
    DateTimeRange? dateTimeRangeCustom,
    List<ConditionOrderStatusEnum>? conditionOrderStatus,
    List<ConditionOrderTypeEnum>? conditionOrderTypes,
    DateTimeRange? dateTimeOrderTime,
    DateTimeRange? dateTimeExpirationDate,
  }) {
    return FilterNormalParam(
      orderStatus: orderStatus ?? this.orderStatus,
      orderType: orderType ?? this.orderType,
      symbol: symbol ?? this.symbol,
      subAccountModel: subAccountModel ?? this.subAccountModel,
      timeFilter: timeFilter ?? this.timeFilter,
      dateTimeRangeCustom: dateTimeRangeCustom ?? this.dateTimeRangeCustom,
      conditionOrderStatus: conditionOrderStatus ?? this.conditionOrderStatus,
      conditionOrderTypes: conditionOrderTypes ?? this.conditionOrderTypes,
      dateTimeOrderTime: dateTimeOrderTime ?? this.dateTimeOrderTime,
      dateTimeExpirationDate:
          dateTimeExpirationDate ?? this.dateTimeExpirationDate,
    );
  }
}
