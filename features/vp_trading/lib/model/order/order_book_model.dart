import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:vp_core/utils/stock_color_utils.dart';
import 'package:vp_trading/screen/order_container/enum/new_order_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';

part 'order_book_model.g.dart';

@JsonSerializable(explicitToJson: true)
class OrderBookModel {
  final String? accountId;
  final String? orderId;
  final String? symbol;
  final String? allowCancel;
  final String? allowAmend;
  final String? side;
  final String? price;
  final String? priceType;
  final String? type;
  final String? via;
  final int? qty;
  final int? execQty;
  final num? execAmt;
  final num? execPrice;
  final int? remainQty;
  final num? remainAmt;
  final String? orderStatus;
  final int? cancelQty;
  final int? adjustQty;
  final String? timeType;
  final String? tradeDate;
  final String? tradeTime;
  final String? parentId;
  final String? username;
  final num? marketPrice;
  final num? ceilingPrice;
  final num? floorPrice;
  final num? refPrice;
  final String? isForced;

  const OrderBookModel({
    this.accountId,
    this.orderId,
    this.symbol,
    this.allowCancel,
    this.allowAmend,
    this.side,
    this.price,
    this.priceType,
    this.type,
    this.via,
    this.qty,
    this.execQty,
    this.execAmt,
    this.execPrice,
    this.remainQty,
    this.remainAmt,
    this.orderStatus,
    this.cancelQty,
    this.adjustQty,
    this.timeType,
    this.tradeDate,
    this.tradeTime,
    this.parentId,
    this.username,
    this.marketPrice,
    this.ceilingPrice,
    this.floorPrice,
    this.refPrice,
    this.isForced,
  });

  factory OrderBookModel.fromJson(Map<String, dynamic> json) =>
      _$OrderBookModelFromJson(json);

  Map<String, dynamic> toJson() => _$OrderBookModelToJson(this);
}

extension OrderBookModelExtension on OrderBookModel {
  OrderStatusEnum get orderStatusEnum =>
      OrderStatusEnumExt.orderStatusFromString(orderStatus);

  NewOrderStatusEnum get newOrderStatusEnum =>
      NewOrderStatusEnum.fromCode(orderStatus ?? '') ?? NewOrderStatusEnum.ps;

  OrderTypeEnum get orderTypeEnum => OrderTypeEnumExt.orderTypeFromString(side);

  Color? get colorPrice {
    if (marketPrice == null) return null;

    return StockColorUtils.colorByPrice(
      referencePrice: refPrice?.toDouble() ?? 0,
      currentPrice: marketPrice ?? 0,
      ceilingPrice: ceilingPrice?.toDouble(),
      floorPrice: floorPrice?.toDouble(),
    );
  }

  // mapping trường via
  String get viaName {
    switch (via) {
      case 'V':
        return 'App NeoInvestPro';
      case 'D':
        return 'neoPro web';
      case 'N':
        return 'NeoAPI';
      case 'F':
        return 'Counter';
      case 'A':
        return 'Tất cả';
      case 'O':
        return 'Web';
      case 'T':
        return 'Tele';
      case 'M':
        return 'Moblie';
      case 'H':
        return 'Home Trading';
      case 'Y':
        return 'Mobile mới';
      case 'Z':
        return 'Web app mới';
      case 'C':
        return 'Cash Service';
      case 'P':
        return 'Copy Trade';
      case 'R':
        return 'ePartner';
      case 'G':
        return 'Lệnh điều kiện';
      case 'W':
        return 'MM';
      case 'L':
        return 'Weath';
      default:
        return '-';
    }
  }

  bool get isEnableEdit => allowAmend == "Y";

  bool get isEnableCancel => allowCancel == "Y";

  String get getNote {
    if (isForced == "Y") {
      return "Lệnh đóng đang xử lý";
    } else {
      return "-";
    }
  }
}
