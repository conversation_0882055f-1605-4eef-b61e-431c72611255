class PreCheckOrderRequest {
  final String instrument;
  final num volume;
  final String order;
  final String type;
  final dynamic price;
  final String buyIn;

  PreCheckOrderRequest({
    required this.instrument,
    required this.volume,
    required this.order,
    required this.type,
    required this.price,
    required this.buyIn,
  });

  Map<String, dynamic> toJson() => {
    PreCheckOrderRequestKey.instrument: instrument,
    PreCheckOrderRequestKey.volume: volume,
    PreCheckOrderRequestKey.order: order,
    PreCheckOrderRequestKey.type: type,
    PreCheckOrderRequestKey.price: price,
    PreCheckOrderRequestKey.buyIn: buyIn,
  };
}

class PreCheckOrderRequestKey {
  static const instrument = 'instrument';
  static const volume = 'qty';
  static const order = 'side';
  static const type = 'type';
  static const price = 'limitPrice';
  static const buyIn = 'buyin';
}
