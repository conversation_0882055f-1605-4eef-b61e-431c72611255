class GtcRequest {
  final String? authType;
  final String tokenId;
  final String transactionId;
  final EngineInput engineInput;

  GtcRequest(this.authType, this.tokenId, this.transactionId, this.engineInput);

  Map<String, dynamic> toJson() => {
        EngineInputKey.authType: authType,
        EngineInputKey.tokenId: tokenId,
        EngineInputKey.transactionId: transactionId,
        EngineInputKey.engineInput: engineInput.toJson(),
      };
}

class EngineInput {
  final String? symbol;
  final String order;
  final double price;
  final num volume;
  final String startDate;
  final String endDate;

  EngineInput(
      {this.symbol,
      required this.order,
      required this.price,
      required this.volume,
      required this.startDate,
      required this.endDate});

  Map<String, dynamic> toJson() => {
        EngineInputKey.symbol: symbol,
        EngineInputKey.volume: volume,
        EngineInputKey.order: order,
        EngineInputKey.price: price,
        EngineInputKey.startDate: startDate,
        EngineInputKey.endDate: endDate,
      };
}

class EngineInputKey {
  static const code = 'code';
  static const authType = 'authType';
  static const tokenId = 'tokenid';
  static const transactionId = 'transactionId';
  static const engineInput = 'engineInput';
  static const symbol = 'symbol';
  static const price = 'quoteprice';
  static const instrument = 'instrument';
  static const volume = 'qty';
  static const order = 'side';
  static const startDate = 'effdate';
  static const endDate = 'expdate';
}
