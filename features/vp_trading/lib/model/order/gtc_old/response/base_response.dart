import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/vp_common.dart';

class BEBaseResponse with BaseResponseError {
  dynamic status;

  dynamic data;

  dynamic httpStatus;

  BEBaseResponse.fromJson(dynamic json) {
    code = json['code'] ?? json['ec'];
    message = json['em'] ?? json['message'];
    status = json['status'] ?? json['s'];
    data = json['data'];
    httpStatus = json['httpStatus'];
  }

  BEBaseResponse({dynamic code, String? message, this.status, this.data}) {
    this.code = int.tryParse(code.toString())?.abs();
    this.message = message;
  }

  bool isSuccess() {
    return code?.toString() == '0' ||
        code?.toString() == '200' ||
        status?.toString() == '1' ||
        status?.toString() == 'ok';
  }

  bool is503() {
    return (httpStatus ?? '') == '503';
  }

  bool isCancelCentralize() {
    return status == closeInputOTPErrorCode;
  }

  bool isEmpty() {
    return code?.toString() == 'IVBERR02';
  }
}
