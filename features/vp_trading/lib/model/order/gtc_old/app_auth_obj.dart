class AppAuthObj {
  String? transactionId;

  String? tokenid;

  String? code;

  String? type;

  bool? checkShowGuideSmartOTP;

  bool? autoFillPin;

  AppAuthObj(
      {this.transactionId,
      this.tokenid,
      this.code,
      this.type,
      this.autoFillPin = false,
      this.checkShowGuideSmartOTP = false});

  AppAuthObj.fromJson(Map<String, dynamic> json)
      : transactionId = json['transactionId'],
        tokenid = json['tokenid'],
        type = json['authType'];
}
