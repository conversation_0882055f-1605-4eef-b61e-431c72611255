class CheckModel {
  String? tokenid;
  String? transactionId;
  String? authtype;

  CheckModel({this.tokenid, this.transactionId, this.authtype});

  CheckModel.fromJson(Map<String, dynamic> json) {
    tokenid = json['tokenid'];
    transactionId = json['transactionId'];
    authtype = json['authtype'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data =  <String, dynamic>{};
    data['tokenid'] = tokenid;
    data['transactionId'] = transactionId;
    data['authtype'] = authtype;
    return data;
  }
}