// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_book_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderBookModel _$OrderBookModelFromJson(Map<String, dynamic> json) =>
    OrderBookModel(
      accountId: json['accountId'] as String?,
      orderId: json['orderId'] as String?,
      symbol: json['symbol'] as String?,
      allowCancel: json['allowCancel'] as String?,
      allowAmend: json['allowAmend'] as String?,
      side: json['side'] as String?,
      price: json['price'] as String?,
      priceType: json['priceType'] as String?,
      type: json['type'] as String?,
      via: json['via'] as String?,
      qty: (json['qty'] as num?)?.toInt(),
      execQty: (json['execQty'] as num?)?.toInt(),
      execAmt: json['execAmt'] as num?,
      execPrice: json['execPrice'] as num?,
      remainQty: (json['remainQty'] as num?)?.toInt(),
      remainAmt: json['remainAmt'] as num?,
      orderStatus: json['orderStatus'] as String?,
      cancelQty: (json['cancelQty'] as num?)?.toInt(),
      adjustQty: (json['adjustQty'] as num?)?.toInt(),
      timeType: json['timeType'] as String?,
      tradeDate: json['tradeDate'] as String?,
      tradeTime: json['tradeTime'] as String?,
      parentId: json['parentId'] as String?,
      username: json['username'] as String?,
      marketPrice: json['marketPrice'] as num?,
      ceilingPrice: json['ceilingPrice'] as num?,
      floorPrice: json['floorPrice'] as num?,
      refPrice: json['refPrice'] as num?,
      isForced: json['isForced'] as String?,
    );

Map<String, dynamic> _$OrderBookModelToJson(OrderBookModel instance) =>
    <String, dynamic>{
      'accountId': instance.accountId,
      'orderId': instance.orderId,
      'symbol': instance.symbol,
      'allowCancel': instance.allowCancel,
      'allowAmend': instance.allowAmend,
      'side': instance.side,
      'price': instance.price,
      'priceType': instance.priceType,
      'type': instance.type,
      'via': instance.via,
      'qty': instance.qty,
      'execQty': instance.execQty,
      'execAmt': instance.execAmt,
      'execPrice': instance.execPrice,
      'remainQty': instance.remainQty,
      'remainAmt': instance.remainAmt,
      'orderStatus': instance.orderStatus,
      'cancelQty': instance.cancelQty,
      'adjustQty': instance.adjustQty,
      'timeType': instance.timeType,
      'tradeDate': instance.tradeDate,
      'tradeTime': instance.tradeTime,
      'parentId': instance.parentId,
      'username': instance.username,
      'marketPrice': instance.marketPrice,
      'ceilingPrice': instance.ceilingPrice,
      'floorPrice': instance.floorPrice,
      'refPrice': instance.refPrice,
      'isForced': instance.isForced,
    };
