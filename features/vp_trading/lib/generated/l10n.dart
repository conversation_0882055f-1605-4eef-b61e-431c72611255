// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class VPTradingLocalize {
  VPTradingLocalize();

  static VPTradingLocalize? _current;

  static VPTradingLocalize get current {
    assert(
      _current != null,
      'No instance of VPTradingLocalize was loaded. Try to initialize the VPTradingLocalize delegate before accessing VPTradingLocalize.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<VPTradingLocalize> load(Locale locale) {
    final name =
        (locale.countryCode?.isEmpty ?? false)
            ? locale.languageCode
            : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = VPTradingLocalize();
      VPTradingLocalize._current = instance;

      return instance;
    });
  }

  static VPTradingLocalize of(BuildContext context) {
    final instance = VPTradingLocalize.maybeOf(context);
    assert(
      instance != null,
      'No instance of VPTradingLocalize present in the widget tree. Did you add VPTradingLocalize.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static VPTradingLocalize? maybeOf(BuildContext context) {
    return Localizations.of<VPTradingLocalize>(context, VPTradingLocalize);
  }

  /// `Price list`
  String get trading_price_list {
    return Intl.message(
      'Price list',
      name: 'trading_price_list',
      desc: '',
      args: [],
    );
  }

  /// `Category`
  String get trading_category {
    return Intl.message(
      'Category',
      name: 'trading_category',
      desc: '',
      args: [],
    );
  }

  /// `Command history`
  String get trading_command_history {
    return Intl.message(
      'Command history',
      name: 'trading_command_history',
      desc: '',
      args: [],
    );
  }

  /// `Utilities`
  String get trading_utilities {
    return Intl.message(
      'Utilities',
      name: 'trading_utilities',
      desc: '',
      args: [],
    );
  }

  /// `Place a stock order`
  String get trading_place_stock_order {
    return Intl.message(
      'Place a stock order',
      name: 'trading_place_stock_order',
      desc: '',
      args: [],
    );
  }

  /// `Buy order`
  String get trading_buy_order {
    return Intl.message(
      'Buy order',
      name: 'trading_buy_order',
      desc: '',
      args: [],
    );
  }

  /// `Sell order`
  String get trading_sell_order {
    return Intl.message(
      'Sell order',
      name: 'trading_sell_order',
      desc: '',
      args: [],
    );
  }

  /// `Buy`
  String get trading_buy {
    return Intl.message('Buy', name: 'trading_buy', desc: '', args: []);
  }

  /// `Sell`
  String get trading_sell {
    return Intl.message('Sell', name: 'trading_sell', desc: '', args: []);
  }

  /// `TK thường`
  String get trading_ordinary {
    return Intl.message(
      'TK thường',
      name: 'trading_ordinary',
      desc: '',
      args: [],
    );
  }

  /// `Tiểu khoản thường`
  String get trading_ordinary_full {
    return Intl.message(
      'Tiểu khoản thường',
      name: 'trading_ordinary_full',
      desc: '',
      args: [],
    );
  }

  /// `TK ký quỹ`
  String get trading_deposit {
    return Intl.message(
      'TK ký quỹ',
      name: 'trading_deposit',
      desc: '',
      args: [],
    );
  }

  /// `Tiểu khoản ký quỹ`
  String get trading_deposit_full {
    return Intl.message(
      'Tiểu khoản ký quỹ',
      name: 'trading_deposit_full',
      desc: '',
      args: [],
    );
  }

  /// `Stock code`
  String get trading_sCode {
    return Intl.message(
      'Stock code',
      name: 'trading_sCode',
      desc: '',
      args: [],
    );
  }

  /// `Giá`
  String get trading_place_order_price {
    return Intl.message(
      'Giá',
      name: 'trading_place_order_price',
      desc: '',
      args: [],
    );
  }

  /// `Stock`
  String get trading_stock {
    return Intl.message('Stock', name: 'trading_stock', desc: '', args: []);
  }

  /// `Normal`
  String get trading_normal {
    return Intl.message('Normal', name: 'trading_normal', desc: '', args: []);
  }

  /// `Transaction history`
  String get trading_transaction_history {
    return Intl.message(
      'Transaction history',
      name: 'trading_transaction_history',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh thường`
  String get trading_order_type_lo {
    return Intl.message(
      'Lệnh thường',
      name: 'trading_order_type_lo',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh Buy-in`
  String get trading_order_type_buyin {
    return Intl.message(
      'Lệnh Buy-in',
      name: 'trading_order_type_buyin',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh ATO`
  String get trading_order_type_ato {
    return Intl.message(
      'Lệnh ATO',
      name: 'trading_order_type_ato',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh ATC`
  String get trading_order_type_atc {
    return Intl.message(
      'Lệnh ATC',
      name: 'trading_order_type_atc',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh MP`
  String get trading_order_type_mp {
    return Intl.message(
      'Lệnh MP',
      name: 'trading_order_type_mp',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh MAK`
  String get trading_order_type_mak {
    return Intl.message(
      'Lệnh MAK',
      name: 'trading_order_type_mak',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh MOK`
  String get trading_order_type_mok {
    return Intl.message(
      'Lệnh MOK',
      name: 'trading_order_type_mok',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh MTL`
  String get trading_order_type_mtl {
    return Intl.message(
      'Lệnh MTL',
      name: 'trading_order_type_mtl',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh PLO`
  String get trading_order_type_plo {
    return Intl.message(
      'Lệnh PLO',
      name: 'trading_order_type_plo',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh điều kiện`
  String get trading_order_type_condition {
    return Intl.message(
      'Lệnh điều kiện',
      name: 'trading_order_type_condition',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh GTC`
  String get trading_order_type_gtc {
    return Intl.message(
      'Lệnh GTC',
      name: 'trading_order_type_gtc',
      desc: '',
      args: [],
    );
  }

  /// `Không có kết quả nào phù hợp với bộ lọc. Vui lòng thay đổi điều kiện lọc và thử lại`
  String get trading_no_filter {
    return Intl.message(
      'Không có kết quả nào phù hợp với bộ lọc. Vui lòng thay đổi điều kiện lọc và thử lại',
      name: 'trading_no_filter',
      desc: '',
      args: [],
    );
  }

  /// `Hiện tại không có dữ liệu`
  String get trading_no_data {
    return Intl.message(
      'Hiện tại không có dữ liệu',
      name: 'trading_no_data',
      desc: '',
      args: [],
    );
  }

  /// `Mã`
  String get trading_code_title {
    return Intl.message('Mã', name: 'trading_code_title', desc: '', args: []);
  }

  /// `Giá đặt/\nGiá khớp`
  String get trading_bid_price_title {
    return Intl.message(
      'Giá đặt/\nGiá khớp',
      name: 'trading_bid_price_title',
      desc: '',
      args: [],
    );
  }

  /// `KL đặt/\nKL khớp`
  String get trading_vol_title {
    return Intl.message(
      'KL đặt/\nKL khớp',
      name: 'trading_vol_title',
      desc: '',
      args: [],
    );
  }

  /// `Trạng thái`
  String get trading_status_title {
    return Intl.message(
      'Trạng thái',
      name: 'trading_status_title',
      desc: '',
      args: [],
    );
  }

  /// `Wait for send`
  String get trading_waiting_send {
    return Intl.message(
      'Wait for send',
      name: 'trading_waiting_send',
      desc: '',
      args: [],
    );
  }

  /// `Sending`
  String get trading_sending {
    return Intl.message('Sending', name: 'trading_sending', desc: '', args: []);
  }

  /// `Matched`
  String get trading_party_trade {
    return Intl.message(
      'Matched',
      name: 'trading_party_trade',
      desc: '',
      args: [],
    );
  }

  /// `Waiting`
  String get trading_waiting {
    return Intl.message('Waiting', name: 'trading_waiting', desc: '', args: []);
  }

  /// `Edited`
  String get trading_edited {
    return Intl.message('Edited', name: 'trading_edited', desc: '', args: []);
  }

  /// `Cancelled`
  String get trading_cancelled {
    return Intl.message(
      'Cancelled',
      name: 'trading_cancelled',
      desc: '',
      args: [],
    );
  }

  /// `Cancelled`
  String get trading_cancelled_2 {
    return Intl.message(
      'Cancelled',
      name: 'trading_cancelled_2',
      desc: '',
      args: [],
    );
  }

  /// `Sent`
  String get trading_sent {
    return Intl.message('Sent', name: 'trading_sent', desc: '', args: []);
  }

  /// `Partially matched`
  String get trading_matched_partial {
    return Intl.message(
      'Partially matched',
      name: 'trading_matched_partial',
      desc: '',
      args: [],
    );
  }

  /// `Match all`
  String get trading_match_all {
    return Intl.message(
      'Match all',
      name: 'trading_match_all',
      desc: '',
      args: [],
    );
  }

  /// `Rejected`
  String get trading_rejected {
    return Intl.message(
      'Rejected',
      name: 'trading_rejected',
      desc: '',
      args: [],
    );
  }

  /// `Cancelling`
  String get trading_cancelling {
    return Intl.message(
      'Cancelling',
      name: 'trading_cancelling',
      desc: '',
      args: [],
    );
  }

  /// `Modifying`
  String get trading_modifying {
    return Intl.message(
      'Modifying',
      name: 'trading_modifying',
      desc: '',
      args: [],
    );
  }

  /// `Modified`
  String get trading_modified {
    return Intl.message(
      'Modified',
      name: 'trading_modified',
      desc: '',
      args: [],
    );
  }

  /// `Expired`
  String get trading_expired {
    return Intl.message('Expired', name: 'trading_expired', desc: '', args: []);
  }

  /// `Expire`
  String get trading_expire {
    return Intl.message('Expire', name: 'trading_expire', desc: '', args: []);
  }

  /// `Sub command`
  String get trading_sub_command {
    return Intl.message(
      'Sub command',
      name: 'trading_sub_command',
      desc: '',
      args: [],
    );
  }

  /// `Command status`
  String get trading_command_status {
    return Intl.message(
      'Command status',
      name: 'trading_command_status',
      desc: '',
      args: [],
    );
  }

  /// `Sub-account`
  String get trading_sub_account {
    return Intl.message(
      'Sub-account',
      name: 'trading_sub_account',
      desc: '',
      args: [],
    );
  }

  /// `Stock code`
  String get trading_history_stock_code {
    return Intl.message(
      'Stock code',
      name: 'trading_history_stock_code',
      desc: '',
      args: [],
    );
  }

  /// `Stock type`
  String get trading_stock_type {
    return Intl.message(
      'Stock type',
      name: 'trading_stock_type',
      desc: '',
      args: [],
    );
  }

  /// `Command type`
  String get trading_command_type {
    return Intl.message(
      'Command type',
      name: 'trading_command_type',
      desc: '',
      args: [],
    );
  }

  /// `Order price`
  String get trading_order_price {
    return Intl.message(
      'Order price',
      name: 'trading_order_price',
      desc: '',
      args: [],
    );
  }

  /// `Order time`
  String get trading_order_time {
    return Intl.message(
      'Order time',
      name: 'trading_order_time',
      desc: '',
      args: [],
    );
  }

  /// `Joint volume`
  String get trading_joint_volume {
    return Intl.message(
      'Joint volume',
      name: 'trading_joint_volume',
      desc: '',
      args: [],
    );
  }

  /// `Average matching price`
  String get trading_average_matching_price {
    return Intl.message(
      'Average matching price',
      name: 'trading_average_matching_price',
      desc: '',
      args: [],
    );
  }

  /// `Order value`
  String get trading_order_value_2 {
    return Intl.message(
      'Order value',
      name: 'trading_order_value_2',
      desc: '',
      args: [],
    );
  }

  /// `Order value`
  String get trading_order_value {
    return Intl.message(
      'Order value',
      name: 'trading_order_value',
      desc: '',
      args: [],
    );
  }

  /// `Giá trị`
  String get trading_value {
    return Intl.message('Giá trị', name: 'trading_value', desc: '', args: []);
  }

  /// `Status`
  String get trading_status {
    return Intl.message('Status', name: 'trading_status', desc: '', args: []);
  }

  /// `Edit order`
  String get trading_edit_order {
    return Intl.message(
      'Edit order',
      name: 'trading_edit_order',
      desc: '',
      args: [],
    );
  }

  /// `Cancel order`
  String get trading_cancel_order {
    return Intl.message(
      'Cancel order',
      name: 'trading_cancel_order',
      desc: '',
      args: [],
    );
  }

  /// `Giá khớp`
  String get trading_price_price {
    return Intl.message(
      'Giá khớp',
      name: 'trading_price_price',
      desc: '',
      args: [],
    );
  }

  /// `Selected`
  String get trading_selected {
    return Intl.message(
      'Selected',
      name: 'trading_selected',
      desc: '',
      args: [],
    );
  }

  /// `Wait for send`
  String get trading_waiting_to_send {
    return Intl.message(
      'Wait for send',
      name: 'trading_waiting_to_send',
      desc: '',
      args: [],
    );
  }

  /// `Lưu thông tin lệnh cho lần sau`
  String get trading_save_command {
    return Intl.message(
      'Lưu thông tin lệnh cho lần sau',
      name: 'trading_save_command',
      desc: '',
      args: [],
    );
  }

  /// `month`
  String get trading_month {
    return Intl.message('month', name: 'trading_month', desc: '', args: []);
  }

  /// `year`
  String get trading_year {
    return Intl.message('year', name: 'trading_year', desc: '', args: []);
  }

  /// `Custom`
  String get trading_custom {
    return Intl.message('Custom', name: 'trading_custom', desc: '', args: []);
  }

  /// `Sub-account`
  String get trading_sub_account_hint {
    return Intl.message(
      'Sub-account',
      name: 'trading_sub_account_hint',
      desc: '',
      args: [],
    );
  }

  /// `Status`
  String get trading_status_hint {
    return Intl.message(
      'Status',
      name: 'trading_status_hint',
      desc: '',
      args: [],
    );
  }

  /// `No data`
  String get trading_no_data_message {
    return Intl.message(
      'No data',
      name: 'trading_no_data_message',
      desc: '',
      args: [],
    );
  }

  /// `Order cancelled successfully`
  String get trading_cancel_order_success {
    return Intl.message(
      'Order cancelled successfully',
      name: 'trading_cancel_order_success',
      desc: '',
      args: [],
    );
  }

  /// `Order updated successfully`
  String get trading_update_order_success {
    return Intl.message(
      'Order updated successfully',
      name: 'trading_update_order_success',
      desc: '',
      args: [],
    );
  }

  /// `Matched price`
  String get trading_matched_price {
    return Intl.message(
      'Matched price',
      name: 'trading_matched_price',
      desc: '',
      args: [],
    );
  }

  /// `Order value`
  String get trading_order_value_title {
    return Intl.message(
      'Order value',
      name: 'trading_order_value_title',
      desc: '',
      args: [],
    );
  }

  /// `Cancel order`
  String get trading_cancel_order_title {
    return Intl.message(
      'Cancel order',
      name: 'trading_cancel_order_title',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to cancel the selected order?`
  String get trading_cancel_order_confirm_message {
    return Intl.message(
      'Are you sure you want to cancel the selected order?',
      name: 'trading_cancel_order_confirm_message',
      desc: '',
      args: [],
    );
  }

  /// `Close`
  String get trading_close {
    return Intl.message('Close', name: 'trading_close', desc: '', args: []);
  }

  /// `Buy order`
  String get trading_buy_order_title {
    return Intl.message(
      'Buy order',
      name: 'trading_buy_order_title',
      desc: '',
      args: [],
    );
  }

  /// `Buying power`
  String get trading_buying_power {
    return Intl.message(
      'Buying power',
      name: 'trading_buying_power',
      desc: '',
      args: [],
    );
  }

  /// `Price`
  String get trading_price {
    return Intl.message('Price', name: 'trading_price', desc: '', args: []);
  }

  /// `Volume`
  String get trading_volume {
    return Intl.message('Volume', name: 'trading_volume', desc: '', args: []);
  }

  /// `Maximum buy volume`
  String get trading_max_volume {
    return Intl.message(
      'Maximum buy volume',
      name: 'trading_max_volume',
      desc: '',
      args: [],
    );
  }

  /// `Maximum sell volume`
  String get trading_max_volume_sell {
    return Intl.message(
      'Maximum sell volume',
      name: 'trading_max_volume_sell',
      desc: '',
      args: [],
    );
  }

  /// `Edit order`
  String get trading_edit_order_button {
    return Intl.message(
      'Edit order',
      name: 'trading_edit_order_button',
      desc: '',
      args: [],
    );
  }

  /// `Status`
  String get trading_status_filter_title {
    return Intl.message(
      'Status',
      name: 'trading_status_filter_title',
      desc: '',
      args: [],
    );
  }

  /// `All`
  String get trading_all {
    return Intl.message('All', name: 'trading_all', desc: '', args: []);
  }

  /// `Reset`
  String get trading_reset {
    return Intl.message('Reset', name: 'trading_reset', desc: '', args: []);
  }

  /// `Apply`
  String get trading_apply {
    return Intl.message('Apply', name: 'trading_apply', desc: '', args: []);
  }

  /// `Search by stock code`
  String get trading_search_by_stock_code {
    return Intl.message(
      'Search by stock code',
      name: 'trading_search_by_stock_code',
      desc: '',
      args: [],
    );
  }

  /// `Transaction type`
  String get trading_transaction_type {
    return Intl.message(
      'Transaction type',
      name: 'trading_transaction_type',
      desc: '',
      args: [],
    );
  }

  /// `Order Type`
  String get trading_order_type_filter {
    return Intl.message(
      'Order Type',
      name: 'trading_order_type_filter',
      desc: '',
      args: [],
    );
  }

  /// `Time`
  String get trading_time {
    return Intl.message('Time', name: 'trading_time', desc: '', args: []);
  }

  /// `Waiting for match`
  String get trading_waiting_match {
    return Intl.message(
      'Waiting for match',
      name: 'trading_waiting_match',
      desc: '',
      args: [],
    );
  }

  /// `Processing`
  String get trading_processing {
    return Intl.message(
      'Processing',
      name: 'trading_processing',
      desc: '',
      args: [],
    );
  }

  /// `Matched`
  String get trading_matched_complete {
    return Intl.message(
      'Matched',
      name: 'trading_matched_complete',
      desc: '',
      args: [],
    );
  }

  /// `Status: All`
  String get trading_status_all {
    return Intl.message(
      'Status: All',
      name: 'trading_status_all',
      desc: '',
      args: [],
    );
  }

  /// `Status: Selected`
  String get trading_status_selected {
    return Intl.message(
      'Status: Selected',
      name: 'trading_status_selected',
      desc: '',
      args: [],
    );
  }

  /// `List of pending orders`
  String get trading_pending_order {
    return Intl.message(
      'List of pending orders',
      name: 'trading_pending_order',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh tranh mua bán tại mức giá mở cửa`
  String get trading_order_description_ato {
    return Intl.message(
      'Lệnh tranh mua bán tại mức giá mở cửa',
      name: 'trading_order_description_ato',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh đặt mua - bán chứng khoán với bất kỳ mức giá nào trong thời gian giao dịch`
  String get trading_order_description_mp {
    return Intl.message(
      'Lệnh đặt mua - bán chứng khoán với bất kỳ mức giá nào trong thời gian giao dịch',
      name: 'trading_order_description_mp',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh thị trường khớp và huỷ tức có thể thực hiện toàn bộ hoặc một phần phần còn lại sẽ bị huỷ ngay sau khi khớp lệnh`
  String get trading_order_description_mak {
    return Intl.message(
      'Lệnh thị trường khớp và huỷ tức có thể thực hiện toàn bộ hoặc một phần phần còn lại sẽ bị huỷ ngay sau khi khớp lệnh',
      name: 'trading_order_description_mak',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh thị trường khớp toàn bộ hoặc huỷ nếu không thực hiện được toàn bộ thì bị huỷ ngay sau khi nhập`
  String get trading_order_description_mok {
    return Intl.message(
      'Lệnh thị trường khớp toàn bộ hoặc huỷ nếu không thực hiện được toàn bộ thì bị huỷ ngay sau khi nhập',
      name: 'trading_order_description_mok',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh thị trường giới hạn nếu không thực hiện được toàn bộ thì phần còn lại chuyển thành lệnh LO và áp dụng các quy định về sửa huỷ đối với lệnh LO`
  String get trading_order_description_mtl {
    return Intl.message(
      'Lệnh thị trường giới hạn nếu không thực hiện được toàn bộ thì phần còn lại chuyển thành lệnh LO và áp dụng các quy định về sửa huỷ đối với lệnh LO',
      name: 'trading_order_description_mtl',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh đặt mua - bán chứng khoán theo giá mong muốn`
  String get trading_order_description_lo {
    return Intl.message(
      'Lệnh đặt mua - bán chứng khoán theo giá mong muốn',
      name: 'trading_order_description_lo',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh bán áp dụng với các mã chứng khoán trong phiên Buy-in từ 8h45 - 9h00`
  String get trading_order_description_buyin {
    return Intl.message(
      'Lệnh bán áp dụng với các mã chứng khoán trong phiên Buy-in từ 8h45 - 9h00',
      name: 'trading_order_description_buyin',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh mua hoặc lệnh bán được thực hiện tại thời điểm giá đang đóng cửa`
  String get trading_order_description_atc {
    return Intl.message(
      'Lệnh mua hoặc lệnh bán được thực hiện tại thời điểm giá đang đóng cửa',
      name: 'trading_order_description_atc',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh mua hoặc bán chứng khoán tại mức giá đóng cửa sau khi kết thúc phiên ATC`
  String get trading_order_description_plo {
    return Intl.message(
      'Lệnh mua hoặc bán chứng khoán tại mức giá đóng cửa sau khi kết thúc phiên ATC',
      name: 'trading_order_description_plo',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh được kích hoạt khi giá đặt lệnh nằm trong khoảng trần - sàn của ngày giao dịch có hiệu lực trong khoảng thời gian thiết lập`
  String get trading_order_description_gtc {
    return Intl.message(
      'Lệnh được kích hoạt khi giá đặt lệnh nằm trong khoảng trần - sàn của ngày giao dịch có hiệu lực trong khoảng thời gian thiết lập',
      name: 'trading_order_description_gtc',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo điều kiện NĐT cài đặt`
  String get trading_order_command_description {
    return Intl.message(
      'Lệnh được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo điều kiện NĐT cài đặt',
      name: 'trading_order_command_description',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh bán được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo kỳ vọng chốt lời của NĐT`
  String get trading_take_profit_command_description {
    return Intl.message(
      'Lệnh bán được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo kỳ vọng chốt lời của NĐT',
      name: 'trading_take_profit_command_description',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh bán được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động đến điểm cắt lỗ của NĐT`
  String get traing_stop_loss_command_description {
    return Intl.message(
      'Lệnh bán được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động đến điểm cắt lỗ của NĐT',
      name: 'traing_stop_loss_command_description',
      desc: '',
      args: [],
    );
  }

  /// `Lệnh chờ`
  String get trading_waiting_command {
    return Intl.message(
      'Lệnh chờ',
      name: 'trading_waiting_command',
      desc: '',
      args: [],
    );
  }

  /// `Cắt lỗ`
  String get trading_stop_loss {
    return Intl.message(
      'Cắt lỗ',
      name: 'trading_stop_loss',
      desc: '',
      args: [],
    );
  }

  /// `Chốt lời`
  String get trading_take_profit {
    return Intl.message(
      'Chốt lời',
      name: 'trading_take_profit',
      desc: '',
      args: [],
    );
  }

  /// `Stock Code`
  String get trading_stock_code {
    return Intl.message(
      'Stock Code',
      name: 'trading_stock_code',
      desc: '',
      args: [],
    );
  }

  /// `Cost Price`
  String get trading_cost_price {
    return Intl.message(
      'Cost Price',
      name: 'trading_cost_price',
      desc: '',
      args: [],
    );
  }

  /// `Volume`
  String get trading_volume_title {
    return Intl.message(
      'Volume',
      name: 'trading_volume_title',
      desc: '',
      args: [],
    );
  }

  /// `Expected P/L`
  String get trading_expected_profit_loss {
    return Intl.message(
      'Expected P/L',
      name: 'trading_expected_profit_loss',
      desc: '',
      args: [],
    );
  }

  /// `Market Value`
  String get trading_market_value {
    return Intl.message(
      'Market Value',
      name: 'trading_market_value',
      desc: '',
      args: [],
    );
  }

  /// `Capital Value`
  String get trading_capital_value {
    return Intl.message(
      'Capital Value',
      name: 'trading_capital_value',
      desc: '',
      args: [],
    );
  }

  /// `Stock Symbol`
  String get trading_stock_symbol {
    return Intl.message(
      'Stock Symbol',
      name: 'trading_stock_symbol',
      desc: '',
      args: [],
    );
  }

  /// `Total Stock Volume`
  String get trading_total_stock_volume {
    return Intl.message(
      'Total Stock Volume',
      name: 'trading_total_stock_volume',
      desc: '',
      args: [],
    );
  }

  /// `Average Cost Price`
  String get trading_average_cost_price {
    return Intl.message(
      'Average Cost Price',
      name: 'trading_average_cost_price',
      desc: '',
      args: [],
    );
  }

  /// `Average Cost Price`
  String get trading_capital_cost_price {
    return Intl.message(
      'Average Cost Price',
      name: 'trading_capital_cost_price',
      desc: '',
      args: [],
    );
  }

  /// `Volume of holdings`
  String get trading_volume_holding {
    return Intl.message(
      'Volume of holdings',
      name: 'trading_volume_holding',
      desc: '',
      args: [],
    );
  }

  /// `Investment Principal`
  String get trading_investment_principal {
    return Intl.message(
      'Investment Principal',
      name: 'trading_investment_principal',
      desc: '',
      args: [],
    );
  }

  /// `Available Stock Volume`
  String get trading_available_stock_volume {
    return Intl.message(
      'Available Stock Volume',
      name: 'trading_available_stock_volume',
      desc: '',
      args: [],
    );
  }

  /// `Pending Stock`
  String get trading_pending_stock {
    return Intl.message(
      'Pending Stock',
      name: 'trading_pending_stock',
      desc: '',
      args: [],
    );
  }

  /// `Pending Rights`
  String get trading_pending_rights {
    return Intl.message(
      'Pending Rights',
      name: 'trading_pending_rights',
      desc: '',
      args: [],
    );
  }

  /// `Restricted Stock`
  String get trading_restricted_stock {
    return Intl.message(
      'Restricted Stock',
      name: 'trading_restricted_stock',
      desc: '',
      args: [],
    );
  }

  /// `Blocked Stock`
  String get trading_blocked_stock {
    return Intl.message(
      'Blocked Stock',
      name: 'trading_blocked_stock',
      desc: '',
      args: [],
    );
  }

  /// `Collateral Stock`
  String get trading_collateral_stock {
    return Intl.message(
      'Collateral Stock',
      name: 'trading_collateral_stock',
      desc: '',
      args: [],
    );
  }

  /// `Portfolio Weight`
  String get trading_portfolio_weight {
    return Intl.message(
      'Portfolio Weight',
      name: 'trading_portfolio_weight',
      desc: '',
      args: [],
    );
  }

  /// `Holding Portfolio`
  String get trading_holding_portfolio {
    return Intl.message(
      'Holding Portfolio',
      name: 'trading_holding_portfolio',
      desc: '',
      args: [],
    );
  }

  /// `Hiển thị thông báo này trong lần sau`
  String get trading_showMessageNextTime {
    return Intl.message(
      'Hiển thị thông báo này trong lần sau',
      name: 'trading_showMessageNextTime',
      desc: '',
      args: [],
    );
  }

  /// `Cancel all order`
  String get trading_cancel_all_order {
    return Intl.message(
      'Cancel all order',
      name: 'trading_cancel_all_order',
      desc: '',
      args: [],
    );
  }

  /// `Effective time`
  String get trading_effective_time {
    return Intl.message(
      'Effective time',
      name: 'trading_effective_time',
      desc: '',
      args: [],
    );
  }

  /// `Activation conditions`
  String get trading_activation_conditions {
    return Intl.message(
      'Activation conditions',
      name: 'trading_activation_conditions',
      desc: '',
      args: [],
    );
  }

  /// `When there is an event of stock price dilution rights`
  String get trading_condition_dilution_choice {
    return Intl.message(
      'When there is an event of stock price dilution rights',
      name: 'trading_condition_dilution_choice',
      desc: '',
      args: [],
    );
  }

  /// `Take Profit Ratio (%)`
  String get trading_condition_take_profit_rate_percent {
    return Intl.message(
      'Take Profit Ratio (%)',
      name: 'trading_condition_take_profit_rate_percent',
      desc: '',
      args: [],
    );
  }

  /// `Profit margin`
  String get trading_condition_profit_margin {
    return Intl.message(
      'Profit margin',
      name: 'trading_condition_profit_margin',
      desc: '',
      args: [],
    );
  }

  /// `Stop loss ratio (%)`
  String get trading_condition_stop_loss_rate_percent {
    return Intl.message(
      'Stop loss ratio (%)',
      name: 'trading_condition_stop_loss_rate_percent',
      desc: '',
      args: [],
    );
  }

  /// `Stop loss margin`
  String get trading_condition_stop_loss_margin {
    return Intl.message(
      'Stop loss margin',
      name: 'trading_condition_stop_loss_margin',
      desc: '',
      args: [],
    );
  }

  /// `Slippage margin`
  String get trading_condition_slippage_margin {
    return Intl.message(
      'Slippage margin',
      name: 'trading_condition_slippage_margin',
      desc: '',
      args: [],
    );
  }

  /// `Order price invalid`
  String get trading_order_price_invalid {
    return Intl.message(
      'Order price invalid',
      name: 'trading_order_price_invalid',
      desc: '',
      args: [],
    );
  }

  /// `Order take profit`
  String get trading_order_take_profit {
    return Intl.message(
      'Order take profit',
      name: 'trading_order_take_profit',
      desc: '',
      args: [],
    );
  }

  /// `Order stop loss`
  String get trading_order_stop_loss {
    return Intl.message(
      'Order stop loss',
      name: 'trading_order_stop_loss',
      desc: '',
      args: [],
    );
  }

  /// `The order can only be activated once during its validity period.`
  String get trading_conditional_order_popup_note {
    return Intl.message(
      'The order can only be activated once during its validity period.',
      name: 'trading_conditional_order_popup_note',
      desc: '',
      args: [],
    );
  }

  /// `Open position`
  String get derivative_open_position {
    return Intl.message(
      'Open position',
      name: 'derivative_open_position',
      desc: '',
      args: [],
    );
  }

  /// `Conditions`
  String get derivative_activation_conditions {
    return Intl.message(
      'Conditions',
      name: 'derivative_activation_conditions',
      desc: '',
      args: [],
    );
  }

  /// `Buy command: Active price must be less than price`
  String get derivative_active_price_must_be_less_than_price {
    return Intl.message(
      'Buy command: Active price must be less than price',
      name: 'derivative_active_price_must_be_less_than_price',
      desc: '',
      args: [],
    );
  }

  /// `Sell command: Active price must be greater than price`
  String get derivative_active_price_must_be_greater_than_price {
    return Intl.message(
      'Sell command: Active price must be greater than price',
      name: 'derivative_active_price_must_be_greater_than_price',
      desc: '',
      args: [],
    );
  }

  /// `Unpaid interest`
  String get derivative_unpaid_interest {
    return Intl.message(
      'Unpaid interest',
      name: 'derivative_unpaid_interest',
      desc: '',
      args: [],
    );
  }

  /// `Active price`
  String get derivative_active_price {
    return Intl.message(
      'Active price',
      name: 'derivative_active_price',
      desc: '',
      args: [],
    );
  }

  /// `Average price: `
  String get derivative_average_price {
    return Intl.message(
      'Average price: ',
      name: 'derivative_average_price',
      desc: '',
      args: [],
    );
  }

  /// `KL tối đa: `
  String get derivative_maximum_weight {
    return Intl.message(
      'KL tối đa: ',
      name: 'derivative_maximum_weight',
      desc: '',
      args: [],
    );
  }

  /// `Close position success`
  String get derivative_close_position_success {
    return Intl.message(
      'Close position success',
      name: 'derivative_close_position_success',
      desc: '',
      args: [],
    );
  }

  /// `Over the maximum volume`
  String get derivative_over_the_maximum_volume2 {
    return Intl.message(
      'Over the maximum volume',
      name: 'derivative_over_the_maximum_volume2',
      desc: '',
      args: [],
    );
  }

  /// `Order Success`
  String get derivative_order_success {
    return Intl.message(
      'Order Success',
      name: 'derivative_order_success',
      desc: '',
      args: [],
    );
  }

  /// `Close position`
  String get derivative_close_position {
    return Intl.message(
      'Close position',
      name: 'derivative_close_position',
      desc: '',
      args: [],
    );
  }

  /// `Contract Code`
  String get derivative_contract_code {
    return Intl.message(
      'Contract Code',
      name: 'derivative_contract_code',
      desc: '',
      args: [],
    );
  }

  /// `Order type`
  String get derivative_order_type {
    return Intl.message(
      'Order type',
      name: 'derivative_order_type',
      desc: '',
      args: [],
    );
  }

  /// `Mass`
  String get derivative_mass {
    return Intl.message('Mass', name: 'derivative_mass', desc: '', args: []);
  }

  /// `Set price`
  String get derivative_price_order {
    return Intl.message(
      'Set price',
      name: 'derivative_price_order',
      desc: '',
      args: [],
    );
  }

  /// `Show this message next time`
  String get derivative_show_message_next_time {
    return Intl.message(
      'Show this message next time',
      name: 'derivative_show_message_next_time',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get derivative_cancel {
    return Intl.message(
      'Cancel',
      name: 'derivative_cancel',
      desc: '',
      args: [],
    );
  }

  /// `Confirm`
  String get derivative_confirm {
    return Intl.message(
      'Confirm',
      name: 'derivative_confirm',
      desc: '',
      args: [],
    );
  }

  /// `Regular order`
  String get derivative_regular_order {
    return Intl.message(
      'Regular order',
      name: 'derivative_regular_order',
      desc: '',
      args: [],
    );
  }

  /// `Stop Order Command`
  String get derivative_stop_order_command {
    return Intl.message(
      'Stop Order Command',
      name: 'derivative_stop_order_command',
      desc: '',
      args: [],
    );
  }

  /// `Trailing Stop Command`
  String get derivative_trailing_stop_command {
    return Intl.message(
      'Trailing Stop Command',
      name: 'derivative_trailing_stop_command',
      desc: '',
      args: [],
    );
  }

  /// `Stop Loss/ Take Profit Command`
  String get derivative_stop_loss_or_take_profit_command {
    return Intl.message(
      'Stop Loss/ Take Profit Command',
      name: 'derivative_stop_loss_or_take_profit_command',
      desc: '',
      args: [],
    );
  }

  /// `Order accept`
  String get derivative_order_accept {
    return Intl.message(
      'Order accept',
      name: 'derivative_order_accept',
      desc: '',
      args: [],
    );
  }

  /// `Future`
  String get derivative_future {
    return Intl.message(
      'Future',
      name: 'derivative_future',
      desc: '',
      args: [],
    );
  }

  /// `Contract and terms`
  String get derivative_contract {
    return Intl.message(
      'Contract and terms',
      name: 'derivative_contract',
      desc: '',
      args: [],
    );
  }

  /// `Terms and conditions`
  String get derivative_terms_and_conditions {
    return Intl.message(
      'Terms and conditions',
      name: 'derivative_terms_and_conditions',
      desc: '',
      args: [],
    );
  }

  /// `By pressing the button`
  String get derivative_by_pressing_the_button {
    return Intl.message(
      'By pressing the button',
      name: 'derivative_by_pressing_the_button',
      desc: '',
      args: [],
    );
  }

  /// `Complete`
  String get derivative_complete {
    return Intl.message(
      'Complete',
      name: 'derivative_complete',
      desc: '',
      args: [],
    );
  }

  /// `Back`
  String get derivative_back {
    return Intl.message('Back', name: 'derivative_back', desc: '', args: []);
  }

  /// `Safe`
  String get derivative_safe {
    return Intl.message('Safe', name: 'derivative_safe', desc: '', args: []);
  }

  /// `Warning`
  String get derivative_warning {
    return Intl.message(
      'Warning',
      name: 'derivative_warning',
      desc: '',
      args: [],
    );
  }

  /// `Handle`
  String get derivative_handle {
    return Intl.message(
      'Handle',
      name: 'derivative_handle',
      desc: '',
      args: [],
    );
  }

  /// `See Detail`
  String get derivative_see_detail {
    return Intl.message(
      'See Detail',
      name: 'derivative_see_detail',
      desc: '',
      args: [],
    );
  }

  /// `Net assets`
  String get derivative_net_assets {
    return Intl.message(
      'Net assets',
      name: 'derivative_net_assets',
      desc: '',
      args: [],
    );
  }

  /// `Profit/loss the day`
  String get derivative_profit_loss_during_day {
    return Intl.message(
      'Profit/loss the day',
      name: 'derivative_profit_loss_during_day',
      desc: '',
      args: [],
    );
  }

  /// `TSKQ usage rate`
  String get derivative_tkps_usage_rate {
    return Intl.message(
      'TSKQ usage rate',
      name: 'derivative_tkps_usage_rate',
      desc: '',
      args: [],
    );
  }

  /// `Include both settled and unrealized P&L for the day`
  String get derivative_vmamt_content {
    return Intl.message(
      'Include both settled and unrealized P&L for the day',
      name: 'derivative_vmamt_content',
      desc: '',
      args: [],
    );
  }

  /// `Sign Up Success`
  String get derivative_sign_up_success {
    return Intl.message(
      'Sign Up Success',
      name: 'derivative_sign_up_success',
      desc: '',
      args: [],
    );
  }

  /// `About the Market screen`
  String get derivative_back_to_market_screen {
    return Intl.message(
      'About the Market screen',
      name: 'derivative_back_to_market_screen',
      desc: '',
      args: [],
    );
  }

  /// `Amount to be added`
  String get derivative_amount_be_added {
    return Intl.message(
      'Amount to be added',
      name: 'derivative_amount_be_added',
      desc: '',
      args: [],
    );
  }

  /// `Cash value needs to be added so that the KQ usage rate reaches the safe threshold.`
  String get derivative_cash_value_needs_to_be_added {
    return Intl.message(
      'Cash value needs to be added so that the KQ usage rate reaches the safe threshold.',
      name: 'derivative_cash_value_needs_to_be_added',
      desc: '',
      args: [],
    );
  }

  /// `Derivative account is awaiting approval.\nThank you.`
  String get derivative_account_await_approve {
    return Intl.message(
      'Derivative account is awaiting approval.\nThank you.',
      name: 'derivative_account_await_approve',
      desc: '',
      args: [],
    );
  }

  /// `We will notify you once your account is successfully activated. Normally, you can trade after 1 working day. Thank you.`
  String get derivative_we_will_notify_you_account {
    return Intl.message(
      'We will notify you once your account is successfully activated. Normally, you can trade after 1 working day. Thank you.',
      name: 'derivative_we_will_notify_you_account',
      desc: '',
      args: [],
    );
  }

  /// ` ,you acknowledge that you have read, understood and agreed to the above contracts.`
  String get derivative_confirm_and_agree_terms {
    return Intl.message(
      ' ,you acknowledge that you have read, understood and agreed to the above contracts.',
      name: 'derivative_confirm_and_agree_terms',
      desc: '',
      args: [],
    );
  }

  /// `Edit/Cancel`
  String get trading_edit_cancel_title {
    return Intl.message(
      'Edit/Cancel',
      name: 'trading_edit_cancel_title',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to cancel all orders?`
  String get trading_cancel_all_orders_confirmation {
    return Intl.message(
      'Are you sure you want to cancel all orders?',
      name: 'trading_cancel_all_orders_confirmation',
      desc: '',
      args: [],
    );
  }

  /// `Nộp/rút ký quỹ PS`
  String get derivativeDepositOrWithdrawDerivative {
    return Intl.message(
      'Nộp/rút ký quỹ PS',
      name: 'derivativeDepositOrWithdrawDerivative',
      desc: '',
      args: [],
    );
  }

  /// `Sao kê lãi lỗ`
  String get derivativeIncomeStatement {
    return Intl.message(
      'Sao kê lãi lỗ',
      name: 'derivativeIncomeStatement',
      desc: '',
      args: [],
    );
  }

  /// `Lịch sử giao dịch`
  String get derivativeTransactionHistory {
    return Intl.message(
      'Lịch sử giao dịch',
      name: 'derivativeTransactionHistory',
      desc: '',
      args: [],
    );
  }

  /// `Cài đặt SL/TP trên danh mục`
  String get derivativeSetupTPSLInCategory {
    return Intl.message(
      'Cài đặt SL/TP trên danh mục',
      name: 'derivativeSetupTPSLInCategory',
      desc: '',
      args: [],
    );
  }

  /// `Tính năng cho phép cài đặt điều kiện Cắt lỗ/ Chốt lời (SL/TP) ngay trên màn danh mục vị thế mở. Quý khách vui lòng xem chi tiết lệnh con tại sổ lệnh thường hoặc sổ lệnh điều kiện sau khi lệnh được kích hoạt. Xem thêm nguyên tắc hoạt động của lệnh`
  String get derivativeSetupTPSLInCategoryNote {
    return Intl.message(
      'Tính năng cho phép cài đặt điều kiện Cắt lỗ/ Chốt lời (SL/TP) ngay trên màn danh mục vị thế mở. Quý khách vui lòng xem chi tiết lệnh con tại sổ lệnh thường hoặc sổ lệnh điều kiện sau khi lệnh được kích hoạt. Xem thêm nguyên tắc hoạt động của lệnh',
      name: 'derivativeSetupTPSLInCategoryNote',
      desc: '',
      args: [],
    );
  }

  /// ` tại đây.`
  String get derivativeAtHere {
    return Intl.message(
      ' tại đây.',
      name: 'derivativeAtHere',
      desc: '',
      args: [],
    );
  }

  /// `Quý khách chưa có tài khoản phái sinh. Mở tài khoản ngay để trải nghiệm giao dịch nhanh chóng cùng VPBankS.`
  String get derivativeContentNotYetAccountPopup {
    return Intl.message(
      'Quý khách chưa có tài khoản phái sinh. Mở tài khoản ngay để trải nghiệm giao dịch nhanh chóng cùng VPBankS.',
      name: 'derivativeContentNotYetAccountPopup',
      desc: '',
      args: [],
    );
  }

  /// `Thông báo`
  String get derivativeNotification {
    return Intl.message(
      'Thông báo',
      name: 'derivativeNotification',
      desc: '',
      args: [],
    );
  }

  /// `Tài khoản phái sinh đang chờ phê duyệt. Quý khách vui lòng thử lại sau hoặc liên hệ Tổng đài 1900 636679 để được hỗ trợ`
  String get derivativeAccountPendingApprove {
    return Intl.message(
      'Tài khoản phái sinh đang chờ phê duyệt. Quý khách vui lòng thử lại sau hoặc liên hệ Tổng đài 1900 636679 để được hỗ trợ',
      name: 'derivativeAccountPendingApprove',
      desc: '',
      args: [],
    );
  }

  /// `Tài khoản phái sinh đang chờ phê duyệt. Quý khách vui lòng thử lại sau hoặc liên hệ Tổng đài 1900 636679 để được hỗ trợ`
  String get derivativeAccountInvalid {
    return Intl.message(
      'Tài khoản phái sinh đang chờ phê duyệt. Quý khách vui lòng thử lại sau hoặc liên hệ Tổng đài 1900 636679 để được hỗ trợ',
      name: 'derivativeAccountInvalid',
      desc: '',
      args: [],
    );
  }

  /// `Đóng`
  String get derivativeDialogCloseButton {
    return Intl.message(
      'Đóng',
      name: 'derivativeDialogCloseButton',
      desc: '',
      args: [],
    );
  }

  /// `Đăng ký`
  String get derivativeDialogRegisterButton {
    return Intl.message(
      'Đăng ký',
      name: 'derivativeDialogRegisterButton',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<VPTradingLocalize> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'vi'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<VPTradingLocalize> load(Locale locale) =>
      VPTradingLocalize.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
