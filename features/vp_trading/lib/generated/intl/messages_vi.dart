// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a vi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'vi';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "derivativeAccountInvalid": MessageLookupByLibrary.simpleMessage(
      "Tài khoản phái sinh đang chờ phê duyệt. Quý khách vui lòng thử lại sau hoặc liên hệ Tổng đài 1900 636679 để được hỗ trợ",
    ),
    "derivativeAccountPendingApprove": MessageLookupByLibrary.simpleMessage(
      "Tài khoản phái sinh đang chờ phê duyệt. Quý khách vui lòng thử lại sau hoặc liên hệ Tổng đài 1900 636679 để được hỗ trợ",
    ),
    "derivativeAtHere": MessageLookupByLibrary.simpleMessage(" tại đây."),
    "derivativeContentNotYetAccountPopup": MessageLookupByLibrary.simpleMessage(
      "Quý khách chưa có tài khoản phái sinh. Mở tài khoản ngay để trải nghiệm giao dịch nhanh chóng cùng VPBankS.",
    ),
    "derivativeDepositOrWithdrawDerivative":
        MessageLookupByLibrary.simpleMessage("Nộp/rút ký quỹ PS"),
    "derivativeDialogCloseButton": MessageLookupByLibrary.simpleMessage("Đóng"),
    "derivativeDialogRegisterButton": MessageLookupByLibrary.simpleMessage(
      "Đăng ký",
    ),
    "derivativeIncomeStatement": MessageLookupByLibrary.simpleMessage(
      "Sao kê lãi lỗ",
    ),
    "derivativeNotification": MessageLookupByLibrary.simpleMessage("Thông báo"),
    "derivativeSetupTPSLInCategory": MessageLookupByLibrary.simpleMessage(
      "Cài đặt SL/TP trên danh mục",
    ),
    "derivativeSetupTPSLInCategoryNote": MessageLookupByLibrary.simpleMessage(
      "Tính năng cho phép cài đặt điều kiện Cắt lỗ/ Chốt lời (SL/TP) ngay trên màn danh mục vị thế mở. Quý khách vui lòng xem chi tiết lệnh con tại sổ lệnh thường hoặc sổ lệnh điều kiện sau khi lệnh được kích hoạt. Xem thêm nguyên tắc hoạt động của lệnh",
    ),
    "derivativeTransactionHistory": MessageLookupByLibrary.simpleMessage(
      "Lịch sử giao dịch",
    ),
    "derivative_account_await_approve": MessageLookupByLibrary.simpleMessage(
      "Tài khoản phái sinh đang chờ phê duyệt.\nXin cảm ơn.",
    ),
    "derivative_activation_conditions": MessageLookupByLibrary.simpleMessage(
      "Điều kiện",
    ),
    "derivative_active_price": MessageLookupByLibrary.simpleMessage(
      "Giá kích hoạt",
    ),
    "derivative_active_price_must_be_greater_than_price":
        MessageLookupByLibrary.simpleMessage(
          "Lệnh bán: Giá kích hoạt phải lớn hơn giá đặt",
        ),
    "derivative_active_price_must_be_less_than_price":
        MessageLookupByLibrary.simpleMessage(
          "Lệnh mua: Giá kích hoạt phải nhỏ hơn giá đặt",
        ),
    "derivative_amount_be_added": MessageLookupByLibrary.simpleMessage(
      "Số tiền cần bổ sung",
    ),
    "derivative_average_price": MessageLookupByLibrary.simpleMessage(
      "Giá bình quân: ",
    ),
    "derivative_back": MessageLookupByLibrary.simpleMessage("Quay lại"),
    "derivative_back_to_market_screen": MessageLookupByLibrary.simpleMessage(
      "Về màn Thị trường",
    ),
    "derivative_by_pressing_the_button": MessageLookupByLibrary.simpleMessage(
      "Bằng việc ấn nút",
    ),
    "derivative_cancel": MessageLookupByLibrary.simpleMessage("Hủy"),
    "derivative_cash_value_needs_to_be_added":
        MessageLookupByLibrary.simpleMessage(
          "Giá trị tiền mặt cần bổ sung để tỷ lệ sử dụng KQ đạt ngưỡng an toàn.",
        ),
    "derivative_close_position": MessageLookupByLibrary.simpleMessage(
      "Đóng vị thế",
    ),
    "derivative_close_position_success": MessageLookupByLibrary.simpleMessage(
      "Lệnh đặt đã gửi",
    ),
    "derivative_complete": MessageLookupByLibrary.simpleMessage("Xác nhận"),
    "derivative_confirm": MessageLookupByLibrary.simpleMessage("Xác nhận"),
    "derivative_confirm_and_agree_terms": MessageLookupByLibrary.simpleMessage(
      ",bạn xác nhận đã đọc, hiểu rõ và đồng ý với các hợp đồng trên.",
    ),
    "derivative_contract": MessageLookupByLibrary.simpleMessage(
      "Hợp đồng và điều khoản",
    ),
    "derivative_contract_code": MessageLookupByLibrary.simpleMessage(
      "Mã hợp đồng",
    ),
    "derivative_future": MessageLookupByLibrary.simpleMessage("Phái sinh"),
    "derivative_handle": MessageLookupByLibrary.simpleMessage("Xử lý"),
    "derivative_mass": MessageLookupByLibrary.simpleMessage("Khối lượng"),
    "derivative_maximum_weight": MessageLookupByLibrary.simpleMessage(
      "KL tối đa: ",
    ),
    "derivative_net_assets": MessageLookupByLibrary.simpleMessage(
      "Tài sản ròng",
    ),
    "derivative_open_position": MessageLookupByLibrary.simpleMessage(
      "Vị thế mở",
    ),
    "derivative_order_accept": MessageLookupByLibrary.simpleMessage(
      "Xác nhận đặt lệnh",
    ),
    "derivative_order_success": MessageLookupByLibrary.simpleMessage(
      "Lệnh đặt đã gửi",
    ),
    "derivative_order_type": MessageLookupByLibrary.simpleMessage("Loại lệnh"),
    "derivative_over_the_maximum_volume2": MessageLookupByLibrary.simpleMessage(
      "Vượt quá khối lượng tối đa",
    ),
    "derivative_price_order": MessageLookupByLibrary.simpleMessage("Giá đặt"),
    "derivative_profit_loss_during_day": MessageLookupByLibrary.simpleMessage(
      "Lãi/ lỗ trong ngày",
    ),
    "derivative_regular_order": MessageLookupByLibrary.simpleMessage(
      "Lệnh thường",
    ),
    "derivative_safe": MessageLookupByLibrary.simpleMessage("An toàn"),
    "derivative_see_detail": MessageLookupByLibrary.simpleMessage(
      "Xem chi tiết",
    ),
    "derivative_show_message_next_time": MessageLookupByLibrary.simpleMessage(
      "Hiển thị thông báo này trong lần sau",
    ),
    "derivative_sign_up_success": MessageLookupByLibrary.simpleMessage(
      "Đăng ký thành công",
    ),
    "derivative_stop_loss_or_take_profit_command":
        MessageLookupByLibrary.simpleMessage("Lệnh Stop Loss/ Take Profit"),
    "derivative_stop_order_command": MessageLookupByLibrary.simpleMessage(
      "Lệnh Stop Order",
    ),
    "derivative_terms_and_conditions": MessageLookupByLibrary.simpleMessage(
      "Các điều khoản và điều kiện",
    ),
    "derivative_tkps_usage_rate": MessageLookupByLibrary.simpleMessage(
      "Tỷ lệ sử dụng TSKQ",
    ),
    "derivative_trailing_stop_command": MessageLookupByLibrary.simpleMessage(
      "Lệnh Trailing Stop",
    ),
    "derivative_unpaid_interest": MessageLookupByLibrary.simpleMessage(
      "Lãi chưa đóng",
    ),
    "derivative_vmamt_content": MessageLookupByLibrary.simpleMessage(
      "Bao gồm Lãi lỗ đã đóng và Lãi lỗ vị thế chưa đóng trong ngày",
    ),
    "derivative_warning": MessageLookupByLibrary.simpleMessage("Cảnh báo"),
    "derivative_we_will_notify_you_account": MessageLookupByLibrary.simpleMessage(
      "Chúng tôi sẽ thông báo cho quý khách sau khi tài khoản được kích hoạt thành công. Thông thường, Quý khách có thể giao dịch sau 1 ngày làm việc. Xin cảm ơn.",
    ),
    "trading_activation_conditions": MessageLookupByLibrary.simpleMessage(
      "Điều kiện kích hoạt",
    ),
    "trading_all": MessageLookupByLibrary.simpleMessage("Tất cả"),
    "trading_apply": MessageLookupByLibrary.simpleMessage("Áp dụng"),
    "trading_available_stock_volume": MessageLookupByLibrary.simpleMessage(
      "Khối lượng CK khả dụng",
    ),
    "trading_average_cost_price": MessageLookupByLibrary.simpleMessage(
      "Giá vốn trung bình",
    ),
    "trading_average_matching_price": MessageLookupByLibrary.simpleMessage(
      "Giá khớp trung bình",
    ),
    "trading_bid_price_title": MessageLookupByLibrary.simpleMessage(
      "Giá đặt/\nGiá khớp",
    ),
    "trading_blocked_stock": MessageLookupByLibrary.simpleMessage(
      "CK bị phong tỏa",
    ),
    "trading_buy": MessageLookupByLibrary.simpleMessage("Mua"),
    "trading_buy_order": MessageLookupByLibrary.simpleMessage("Lệnh mua"),
    "trading_buy_order_title": MessageLookupByLibrary.simpleMessage("Lệnh mua"),
    "trading_buying_power": MessageLookupByLibrary.simpleMessage("Sức mua"),
    "trading_cancel_all_order": MessageLookupByLibrary.simpleMessage(
      "Huỷ tất cả",
    ),
    "trading_cancel_all_orders_confirmation":
        MessageLookupByLibrary.simpleMessage(
          "Bạn có xác nhận muốn huỷ tất cả lệnh?",
        ),
    "trading_cancel_order": MessageLookupByLibrary.simpleMessage("Hủy lệnh"),
    "trading_cancel_order_confirm_message":
        MessageLookupByLibrary.simpleMessage(
          "Bạn xác nhận muốn huỷ lệnh đã chọn",
        ),
    "trading_cancel_order_success": MessageLookupByLibrary.simpleMessage(
      "Gửi yêu cầu thành công",
    ),
    "trading_cancel_order_title": MessageLookupByLibrary.simpleMessage(
      "Huỷ lệnh",
    ),
    "trading_cancelled": MessageLookupByLibrary.simpleMessage("Đã hủy"),
    "trading_cancelled_2": MessageLookupByLibrary.simpleMessage("Lệnh huỷ"),
    "trading_cancelling": MessageLookupByLibrary.simpleMessage("Đang huỷ"),
    "trading_capital_cost_price": MessageLookupByLibrary.simpleMessage(
      "Giá vốn bình quân",
    ),
    "trading_capital_value": MessageLookupByLibrary.simpleMessage(
      "Giá trị vốn",
    ),
    "trading_category": MessageLookupByLibrary.simpleMessage("Danh mục"),
    "trading_close": MessageLookupByLibrary.simpleMessage("Đóng"),
    "trading_code_title": MessageLookupByLibrary.simpleMessage("Mã"),
    "trading_collateral_stock": MessageLookupByLibrary.simpleMessage(
      "CK cầm cố",
    ),
    "trading_command_history": MessageLookupByLibrary.simpleMessage("Sổ lệnh"),
    "trading_command_status": MessageLookupByLibrary.simpleMessage(
      "Trạng thái lệnh",
    ),
    "trading_command_type": MessageLookupByLibrary.simpleMessage("Loại lệnh"),
    "trading_condition_dilution_choice": MessageLookupByLibrary.simpleMessage(
      "Hủy lệnh khi có sự kiện quyền pha loãng cổ phiếu",
    ),
    "trading_condition_profit_margin": MessageLookupByLibrary.simpleMessage(
      "Biên giá chốt lời",
    ),
    "trading_condition_slippage_margin": MessageLookupByLibrary.simpleMessage(
      "Biên trượt giá",
    ),
    "trading_condition_stop_loss_margin": MessageLookupByLibrary.simpleMessage(
      "Biên giá cắt lỗ",
    ),
    "trading_condition_stop_loss_rate_percent":
        MessageLookupByLibrary.simpleMessage("Tỷ lệ cắt lỗ (%)"),
    "trading_condition_take_profit_rate_percent":
        MessageLookupByLibrary.simpleMessage("Tỷ lệ chốt lời (%)"),
    "trading_conditional_order_popup_note":
        MessageLookupByLibrary.simpleMessage(
          "Lệnh chỉ được kích hoạt 01 lần duy nhất trong thời gian hiệu lực.",
        ),
    "trading_cost_price": MessageLookupByLibrary.simpleMessage("Giá vốn"),
    "trading_custom": MessageLookupByLibrary.simpleMessage("Tùy chọn"),
    "trading_deposit": MessageLookupByLibrary.simpleMessage("TK ký quỹ"),
    "trading_deposit_full": MessageLookupByLibrary.simpleMessage(
      "Tiểu khoản ký quỹ",
    ),
    "trading_edit_cancel_title": MessageLookupByLibrary.simpleMessage(
      "Sửa/Hủy",
    ),
    "trading_edit_order": MessageLookupByLibrary.simpleMessage("Sửa lệnh"),
    "trading_edit_order_button": MessageLookupByLibrary.simpleMessage(
      "Sửa lệnh",
    ),
    "trading_edited": MessageLookupByLibrary.simpleMessage("Đã sửa"),
    "trading_effective_time": MessageLookupByLibrary.simpleMessage(
      "Thời gian hiệu lực",
    ),
    "trading_expected_profit_loss": MessageLookupByLibrary.simpleMessage(
      "Lãi/lỗ dự kiến",
    ),
    "trading_expire": MessageLookupByLibrary.simpleMessage("Hết hạn"),
    "trading_expired": MessageLookupByLibrary.simpleMessage("Hết hiệu lực"),
    "trading_history_stock_code": MessageLookupByLibrary.simpleMessage(
      "Mã cổ phiếu",
    ),
    "trading_holding_portfolio": MessageLookupByLibrary.simpleMessage(
      "Danh mục nắm giữ",
    ),
    "trading_investment_principal": MessageLookupByLibrary.simpleMessage(
      "Gốc đầu tư",
    ),
    "trading_joint_volume": MessageLookupByLibrary.simpleMessage(
      "Khối lượng khớp",
    ),
    "trading_market_value": MessageLookupByLibrary.simpleMessage(
      "Giá trị thị trường",
    ),
    "trading_match_all": MessageLookupByLibrary.simpleMessage("Khớp hết"),
    "trading_matched_complete": MessageLookupByLibrary.simpleMessage(
      "Khớp hết",
    ),
    "trading_matched_partial": MessageLookupByLibrary.simpleMessage(
      "Khớp 1 phần",
    ),
    "trading_matched_price": MessageLookupByLibrary.simpleMessage("Giá khớp"),
    "trading_max_volume": MessageLookupByLibrary.simpleMessage(
      "Khối lượng mua tối đa",
    ),
    "trading_max_volume_sell": MessageLookupByLibrary.simpleMessage(
      "Khối lượng bán tối đa",
    ),
    "trading_modified": MessageLookupByLibrary.simpleMessage("Đã sửa"),
    "trading_modifying": MessageLookupByLibrary.simpleMessage("Đang sửa"),
    "trading_month": MessageLookupByLibrary.simpleMessage("tháng"),
    "trading_no_data": MessageLookupByLibrary.simpleMessage(
      "Hiện tại không có dữ liệu",
    ),
    "trading_no_data_message": MessageLookupByLibrary.simpleMessage(
      "Không có dữ liệu",
    ),
    "trading_no_filter": MessageLookupByLibrary.simpleMessage(
      "Không có kết quả nào phù hợp với bộ lọc. Vui lòng thay đổi điều kiện lọc và thử lại",
    ),
    "trading_normal": MessageLookupByLibrary.simpleMessage("Lệnh thường"),
    "trading_order_command_description": MessageLookupByLibrary.simpleMessage(
      "Lệnh được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo điều kiện NĐT cài đặt",
    ),
    "trading_order_description_atc": MessageLookupByLibrary.simpleMessage(
      "Lệnh mua hoặc lệnh bán được thực hiện tại thời điểm giá đang đóng cửa",
    ),
    "trading_order_description_ato": MessageLookupByLibrary.simpleMessage(
      "Lệnh tranh mua bán tại mức giá mở cửa",
    ),
    "trading_order_description_buyin": MessageLookupByLibrary.simpleMessage(
      "Lệnh bán áp dụng với các mã chứng khoán trong phiên Buy-in từ 8h45 - 9h00",
    ),
    "trading_order_description_gtc": MessageLookupByLibrary.simpleMessage(
      "Lệnh được kích hoạt khi giá đặt lệnh nằm trong khoảng trần - sàn của ngày giao dịch có hiệu lực trong khoảng thời gian thiết lập",
    ),
    "trading_order_description_lo": MessageLookupByLibrary.simpleMessage(
      "Lệnh đặt mua - bán chứng khoán theo giá mong muốn",
    ),
    "trading_order_description_mak": MessageLookupByLibrary.simpleMessage(
      "Lệnh thị trường khớp và huỷ tức có thể thực hiện toàn bộ hoặc một phần phần còn lại sẽ bị huỷ ngay sau khi khớp lệnh",
    ),
    "trading_order_description_mok": MessageLookupByLibrary.simpleMessage(
      "Lệnh thị trường khớp toàn bộ hoặc huỷ nếu không thực hiện được toàn bộ thì bị huỷ ngay sau khi nhập",
    ),
    "trading_order_description_mp": MessageLookupByLibrary.simpleMessage(
      "Lệnh đặt mua - bán chứng khoán với bất kỳ mức giá nào trong thời gian giao dịch",
    ),
    "trading_order_description_mtl": MessageLookupByLibrary.simpleMessage(
      "Lệnh thị trường giới hạn nếu không thực hiện được toàn bộ thì phần còn lại chuyển thành lệnh LO và áp dụng các quy định về sửa huỷ đối với lệnh LO",
    ),
    "trading_order_description_plo": MessageLookupByLibrary.simpleMessage(
      "Lệnh mua hoặc bán chứng khoán tại mức giá đóng cửa sau khi kết thúc phiên ATC",
    ),
    "trading_order_price": MessageLookupByLibrary.simpleMessage("Giá đặt lệnh"),
    "trading_order_price_invalid": MessageLookupByLibrary.simpleMessage(
      "Giá đặt không hợp lệ",
    ),
    "trading_order_stop_loss": MessageLookupByLibrary.simpleMessage(
      "Lệnh cắt lỗ",
    ),
    "trading_order_take_profit": MessageLookupByLibrary.simpleMessage(
      "Lệnh chốt lời",
    ),
    "trading_order_time": MessageLookupByLibrary.simpleMessage(
      "Thời gian đặt lệnh",
    ),
    "trading_order_type_atc": MessageLookupByLibrary.simpleMessage("Lệnh ATC"),
    "trading_order_type_ato": MessageLookupByLibrary.simpleMessage("Lệnh ATO"),
    "trading_order_type_buyin": MessageLookupByLibrary.simpleMessage(
      "Lệnh Buy-in",
    ),
    "trading_order_type_condition": MessageLookupByLibrary.simpleMessage(
      "Lệnh điều kiện",
    ),
    "trading_order_type_filter": MessageLookupByLibrary.simpleMessage(
      "Loại lệnh",
    ),
    "trading_order_type_gtc": MessageLookupByLibrary.simpleMessage("Lệnh GTC"),
    "trading_order_type_lo": MessageLookupByLibrary.simpleMessage(
      "Lệnh thường",
    ),
    "trading_order_type_mak": MessageLookupByLibrary.simpleMessage("Lệnh MAK"),
    "trading_order_type_mok": MessageLookupByLibrary.simpleMessage("Lệnh MOK"),
    "trading_order_type_mp": MessageLookupByLibrary.simpleMessage("Lệnh MP"),
    "trading_order_type_mtl": MessageLookupByLibrary.simpleMessage("Lệnh MTL"),
    "trading_order_type_plo": MessageLookupByLibrary.simpleMessage("Lệnh PLO"),
    "trading_order_value": MessageLookupByLibrary.simpleMessage("Giá trị lệnh"),
    "trading_order_value_2": MessageLookupByLibrary.simpleMessage(
      "Giá trị lệnh đặt",
    ),
    "trading_order_value_title": MessageLookupByLibrary.simpleMessage(
      "Giá trị lệnh",
    ),
    "trading_ordinary": MessageLookupByLibrary.simpleMessage("TK thường"),
    "trading_ordinary_full": MessageLookupByLibrary.simpleMessage(
      "Tiểu khoản thường",
    ),
    "trading_party_trade": MessageLookupByLibrary.simpleMessage("Đã khớp"),
    "trading_pending_order": MessageLookupByLibrary.simpleMessage(
      "Danh sách lệnh chờ khớp",
    ),
    "trading_pending_rights": MessageLookupByLibrary.simpleMessage(
      "Quyền chờ về",
    ),
    "trading_pending_stock": MessageLookupByLibrary.simpleMessage("CK chờ về"),
    "trading_place_order_price": MessageLookupByLibrary.simpleMessage("Giá"),
    "trading_place_stock_order": MessageLookupByLibrary.simpleMessage(
      "Đặt lệnh cổ phiếu",
    ),
    "trading_portfolio_weight": MessageLookupByLibrary.simpleMessage(
      "Tỉ trọng trong danh mục",
    ),
    "trading_price": MessageLookupByLibrary.simpleMessage("Giá"),
    "trading_price_list": MessageLookupByLibrary.simpleMessage("Bảng giá"),
    "trading_price_price": MessageLookupByLibrary.simpleMessage("Giá khớp"),
    "trading_processing": MessageLookupByLibrary.simpleMessage("Đang xử lý"),
    "trading_rejected": MessageLookupByLibrary.simpleMessage("Từ chối"),
    "trading_reset": MessageLookupByLibrary.simpleMessage("Đặt lại"),
    "trading_restricted_stock": MessageLookupByLibrary.simpleMessage(
      "CK bị hạn chế",
    ),
    "trading_sCode": MessageLookupByLibrary.simpleMessage("Mã CK"),
    "trading_save_command": MessageLookupByLibrary.simpleMessage(
      "Lưu thông tin lệnh cho lần sau",
    ),
    "trading_search_by_stock_code": MessageLookupByLibrary.simpleMessage(
      "Tìm kiếm theo mã chứng khoán",
    ),
    "trading_selected": MessageLookupByLibrary.simpleMessage("Đã chọn"),
    "trading_sell": MessageLookupByLibrary.simpleMessage("Bán"),
    "trading_sell_order": MessageLookupByLibrary.simpleMessage("Lệnh bán"),
    "trading_sending": MessageLookupByLibrary.simpleMessage("Đang gửi"),
    "trading_sent": MessageLookupByLibrary.simpleMessage("Đã gửi"),
    "trading_showMessageNextTime": MessageLookupByLibrary.simpleMessage(
      "Hiển thị thông báo này trong lần sau",
    ),
    "trading_status": MessageLookupByLibrary.simpleMessage("Trạng thái"),
    "trading_status_all": MessageLookupByLibrary.simpleMessage(
      "Trạng thái: Tất cả",
    ),
    "trading_status_filter_title": MessageLookupByLibrary.simpleMessage(
      "Trạng thái",
    ),
    "trading_status_hint": MessageLookupByLibrary.simpleMessage("Trạng thái"),
    "trading_status_selected": MessageLookupByLibrary.simpleMessage(
      "Trạng thái: Đã chọn",
    ),
    "trading_status_title": MessageLookupByLibrary.simpleMessage("Trạng thái"),
    "trading_stock": MessageLookupByLibrary.simpleMessage("Cổ phiếu"),
    "trading_stock_code": MessageLookupByLibrary.simpleMessage("Mã CK"),
    "trading_stock_symbol": MessageLookupByLibrary.simpleMessage(
      "Mã chứng khoán",
    ),
    "trading_stock_type": MessageLookupByLibrary.simpleMessage("Lệnh"),
    "trading_stop_loss": MessageLookupByLibrary.simpleMessage("Cắt lỗ"),
    "trading_sub_account": MessageLookupByLibrary.simpleMessage("Tiểu khoản"),
    "trading_sub_account_hint": MessageLookupByLibrary.simpleMessage(
      "Tiểu khoản",
    ),
    "trading_sub_command": MessageLookupByLibrary.simpleMessage("Lệnh con"),
    "trading_take_profit": MessageLookupByLibrary.simpleMessage("Chốt lời"),
    "trading_take_profit_command_description": MessageLookupByLibrary.simpleMessage(
      "Lệnh bán được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động theo kỳ vọng chốt lời của NĐT",
    ),
    "trading_time": MessageLookupByLibrary.simpleMessage("Thời gian"),
    "trading_total_stock_volume": MessageLookupByLibrary.simpleMessage(
      "Tổng khối lượng CK",
    ),
    "trading_transaction_history": MessageLookupByLibrary.simpleMessage(
      "Lịch sử GD",
    ),
    "trading_transaction_type": MessageLookupByLibrary.simpleMessage(
      "Loại giao dịch",
    ),
    "trading_update_order_success": MessageLookupByLibrary.simpleMessage(
      "Gửi yêu cầu thành công",
    ),
    "trading_utilities": MessageLookupByLibrary.simpleMessage("Tiện ích"),
    "trading_value": MessageLookupByLibrary.simpleMessage("Giá trị"),
    "trading_vol_title": MessageLookupByLibrary.simpleMessage(
      "KL đặt/\nKL khớp",
    ),
    "trading_volume": MessageLookupByLibrary.simpleMessage("Khối lượng"),
    "trading_volume_holding": MessageLookupByLibrary.simpleMessage(
      "Khối lượng nắm giữ",
    ),
    "trading_volume_title": MessageLookupByLibrary.simpleMessage("KL"),
    "trading_waiting": MessageLookupByLibrary.simpleMessage("Đang chờ"),
    "trading_waiting_command": MessageLookupByLibrary.simpleMessage("Lệnh chờ"),
    "trading_waiting_match": MessageLookupByLibrary.simpleMessage("Lệnh chờ"),
    "trading_waiting_send": MessageLookupByLibrary.simpleMessage("Chờ gửi"),
    "trading_waiting_to_send": MessageLookupByLibrary.simpleMessage("Chờ gửi"),
    "trading_year": MessageLookupByLibrary.simpleMessage("năm"),
    "traing_stop_loss_command_description": MessageLookupByLibrary.simpleMessage(
      "Lệnh bán được kích hoạt và đẩy vào thị trường với giá đặt lệnh khi giá thị trường biến động đến điểm cắt lỗ của NĐT",
    ),
  };
}
