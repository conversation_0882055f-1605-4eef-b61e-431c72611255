part of 'order_container_cubit.dart';

final class OrderContainerState extends Equatable {
  const OrderContainerState({
    this.currentTabbar = EnumTabbarOrder.normal,
    this.normalFilterParam,
    this.historyFilterParam,
    this.conditionFilterParam,
  });

  final EnumTabbarOrder currentTabbar;
  final FilterNormalParam? normalFilterParam;
  final FilterNormalParam? historyFilterParam;
  final FilterNormalParam? conditionFilterParam;

  @override
  List<Object?> get props => [
    currentTabbar,
    normalFilterParam,
    historyFilterParam,
    conditionFilterParam,
  ];

  OrderContainerState copyWith({
    EnumTabbarOrder? currentTabbar,
    FilterNormalParam? normalFilterParam,
    FilterNormalParam? historyFilterParam,
    FilterNormalParam? conditionFilterParam,
  }) {
    return OrderContainerState(
      currentTabbar: currentTabbar ?? this.currentTabbar,
      normalFilterParam: normalFilterParam ?? this.normalFilterParam,
      historyFilterParam: historyFilterParam ?? this.historyFilterParam,
      conditionFilterParam: conditionFilterParam ?? this.conditionFilterParam,
    );
  }

  FilterNormalParam? get currentFilterParam {
    switch (currentTabbar) {
      case EnumTabbarOrder.normal:
        return normalFilterParam;
      case EnumTabbarOrder.transactionHistory:
        return historyFilterParam;
      case EnumTabbarOrder.conditional:
        return conditionFilterParam;
    }
  }

  bool get hasFilter {
    if (currentTabbar == EnumTabbarOrder.normal) {
      return normalFilterParam?.symbol != null ||
          (normalFilterParam?.orderType != null &&
              normalFilterParam?.orderType != OrderTypeEnum.all);
    }
    if (currentTabbar == EnumTabbarOrder.transactionHistory) {
      return historyFilterParam?.symbol != null ||
          (historyFilterParam?.orderType != null &&
              historyFilterParam?.orderType != OrderTypeEnum.all) ||
          (historyFilterParam?.timeFilter != null &&
              historyFilterParam?.timeFilter != TradingTimeFilterEnum.oneMonth);
    }
    if (currentTabbar == EnumTabbarOrder.conditional) {
      return conditionFilterParam?.symbol != null ||
          (conditionFilterParam?.orderType != null &&
              conditionFilterParam?.orderType != OrderTypeEnum.all) ||
          (conditionFilterParam?.timeFilter != null &&
              conditionFilterParam?.timeFilter !=
                  TradingTimeFilterEnum.oneMonth) ||
          (conditionFilterParam?.conditionOrderTypes != null &&
              conditionFilterParam!.conditionOrderTypes!.isNotEmpty &&
              !(conditionFilterParam!.conditionOrderTypes!.length == 1 &&
                  conditionFilterParam!.conditionOrderTypes!.contains(
                    ConditionOrderTypeEnum.all,
                  ))) ||
          (conditionFilterParam?.dateTimeOrderTime != null) ||
          (conditionFilterParam?.dateTimeExpirationDate != null);
    }
    return historyFilterParam?.symbol != null ||
        (historyFilterParam?.orderType != null &&
            historyFilterParam?.orderType != OrderTypeEnum.all) ||
        (historyFilterParam?.timeFilter != null &&
            historyFilterParam?.timeFilter != TradingTimeFilterEnum.oneMonth);
  }
}
