import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:vp_common/error/show_error.dart';
import 'package:vp_common/utils/app_time_utils.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/repository/command_history_repository.dart';
import 'package:vp_trading/model/common_paging_state/common_paging_state.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/model/request/order/filter_nomal_param.dart';
import 'package:vp_trading/model/request/order/order_book_request.dart';
import 'package:vp_trading/screen/order_container/enum/condition_order_status_enum.dart';
import 'package:vp_trading/screen/order_container/enum/condition_order_type_enum.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';

part 'condition_order_state.dart';

class ConditionOrderCubit extends Cubit<ConditionOrderState> {
  ConditionOrderCubit() : super(const ConditionOrderState());
  final CommandHistoryRepository _repository =
      GetIt.instance<CommandHistoryRepository>();
  void init() {
    emit(
      state.copyWith(
        filterParam: FilterNormalParam(
          subAccountModel: subAccountTypeAll,
          conditionOrderStatus: [ConditionOrderStatusEnum.all],
        ),
        request: state.request.copyWith(
          accountId:
              GetIt.instance<SubAccountCubit>().defaultSubAccount.id ?? "",
        ),
      ),
    );

    loadData();
  }

  Future<void> loadData({bool showLoading = true}) async {
    try {
      if (showLoading) {
        emit(state.copyWith(isLoading: true));
      }
      var accountId = "ALL";
      if (state.filterParam?.subAccountModel?.accountType !=
          SubAccountType.all) {
        accountId = state.filterParam?.subAccountModel?.id ?? "ALL";
      }
      final result = await _repository.getOrderCondition(
        state.request.copyWith(
          accountId: accountId,
          pageNo: 1,
          pageSize: 10000,
        ),
      );

      final items = result.data?.content ?? [];
      final hasMore = items.isNotEmpty && items.length >= state.pageSize;

      emit(
        state.copyWith(
          isLoading: false,
          listItems: items,
          pagingState: state.pagingState.copyWith(
            currentPage: 1,
            hasMore: hasMore,
          ),
        ),
      );
    } catch (e) {
      showError(e);
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: await getErrorMessage(e),
        ),
      );
      debugPrintStack(stackTrace: StackTrace.current);
    }
  }

  // Future<void> loadMore() async {
  //   if (state.isLoading || !state.hasMore) return;

  //   try {
  //     final result = await _commandHistoryRepository.getOrder(
  //       queries: state.request.copyWith(pageNo: state.nextPage),
  //     );

  //     final newItems = result.data?.content ?? [];
  //     final hasMore = newItems.isNotEmpty && newItems.length >= state.pageSize;

  //     emit(
  //       state.copyWith(
  //         listItems: [...state.listItems, ...newItems],
  //         pagingState: state.pagingState.copyWith(
  //           currentPage: state.nextPage,
  //           hasMore: hasMore,
  //         ),
  //       ),
  //     );
  //   } catch (e) {
  //     showError(e);
  //     debugPrintStack(stackTrace: StackTrace.current);
  //   }
  // }

  Future<void> updateFilterStatus(
    List<ConditionOrderStatusEnum> orderStatus,
  ) async {
    emit(
      state.copyWith(
        filterParam: state.filterParam?.copyWith(
          conditionOrderStatus: orderStatus,
        ),
        request: state.request.copyWith(
          orderStatus: orderStatus.map((e) => e.codeRequest).join(','),
        ),
      ),
    );
    await loadData();
  }

  Future<void> updateFilterAccount(SubAccountModel subAccountModel) async {
    emit(
      state.copyWith(
        filterParam: state.filterParam?.copyWith(
          subAccountModel: subAccountModel,
        ),
        request: state.request.copyWith(accountId: subAccountModel.id ?? ""),
      ),
    );
    await loadData();
  }

  Future<void> updateFilter(FilterNormalParam filterParam) async {
    final currentFilter = state.filterParam;
    final newFilter = currentFilter?.copyWith(
      orderType: filterParam.orderType,
      symbol: filterParam.symbol,
      conditionOrderTypes: filterParam.conditionOrderTypes,
      dateTimeOrderTime: filterParam.dateTimeOrderTime,
      dateTimeExpirationDate: filterParam.dateTimeExpirationDate,
    );

    // Convert multiple condition order types to comma-separated string for API
    String? orderTypesString;
    if (filterParam.conditionOrderTypes != null &&
        filterParam.conditionOrderTypes!.isNotEmpty) {
      if (filterParam.conditionOrderTypes!.contains(
        ConditionOrderTypeEnum.all,
      )) {
        orderTypesString = ConditionOrderTypeEnum.all.codeRequest;
      } else {
        orderTypesString = filterParam.conditionOrderTypes!
            .map((e) => e.codeRequest)
            .join(',');
      }
    }

    final request = OrderBookRequest(
      symbol: filterParam.symbol,
      side: filterParam.orderType?.codeRequestCondition,
      orderTypes: orderTypesString,
      fromCreatedAt:
          filterParam.dateTimeOrderTime == null
              ? null
              : AppTimeUtils.formatTime(
                filterParam.dateTimeOrderTime!.start.toString(),
                format: AppTimeUtilsFormat.dateYMD,
              ),
      toCreatedAt:
          filterParam.dateTimeOrderTime == null
              ? null
              : AppTimeUtils.formatTime(
                filterParam.dateTimeOrderTime!.end.toString(),
                format: AppTimeUtilsFormat.dateYMD,
              ),
      fromExpirationDate:
          filterParam.dateTimeExpirationDate == null
              ? null
              : AppTimeUtils.formatTime(
                filterParam.dateTimeExpirationDate!.start.toString(),
                format: AppTimeUtilsFormat.dateYMD,
              ),
      toExpirationDate:
          filterParam.dateTimeExpirationDate == null
              ? null
              : AppTimeUtils.formatTime(
                filterParam.dateTimeExpirationDate!.end.toString(),
                format: AppTimeUtilsFormat.dateYMD,
              ),
    );
    emit(state.copyWith(filterParam: newFilter, request: request));
    await loadData();
  }


  void resetErrorMessage() {
    emit(state.copyWith(errorMessage: null));
  }
}
