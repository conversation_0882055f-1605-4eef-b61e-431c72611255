import 'package:flutter/widgets.dart';
import 'package:vp_common/vp_common.dart' hide AppLocalizationDelegate;
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/repository/command_history_repository.dart';
import 'package:vp_trading/core/repository/derivative_repository.dart';
import 'package:vp_trading/core/repository/derivative_repository_impl.dart';
import 'package:vp_trading/core/repository/holding_portfolio_repository.dart';
import 'package:vp_trading/core/repository/place_order_repository.dart';
import 'package:vp_trading/core/service/command_history_service.dart';
import 'package:vp_trading/core/service/derivative_service.dart';
import 'package:vp_trading/core/service/holding_portfolio_service.dart';
import 'package:vp_trading/core/service/place_order_service.dart';
import 'package:vp_trading/generated/intl/messages_all.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/vp_trading_navigator.dart';

import 'router/trading_router.dart';

class TradingModule implements Module {
  @override
  void injectServices(GetIt service) {
    service.registerLazySingleton<PlaceOrderService>(
      () => PlaceOrderService(
        service(),
        baseUrl: "https://neopro-uat.vpbanks.com.vn",
      ),
    );
    // todo cuong: sửa lại enpoint sau
    service.registerLazySingleton<CommandHistoryService>(
      () => CommandHistoryService(
        service(),
        baseUrl: "https://neopro-uat.vpbanks.com.vn",
      ),
    );
    // todo cuong: sửa lại enpoint sau
    service.registerLazySingleton<HoldingPortfolioService>(
      () => HoldingPortfolioService(
        service(),
        baseUrl: "https://neopro-uat.vpbanks.com.vn",
      ),
    );

    service.registerLazySingleton<DerivativeService>(
      () => DerivativeService(service()),
    );
    service.registerLazySingleton<CommandHistoryRepository>(
      () => CommandHistoryRepositoryImpl(commandHistoryService: service()),
    );
    service.registerLazySingleton<PlaceOrderRepository>(
      () => PlaceOrderRepositoryImpl(
        restClient: service(),
        placeOrderService: service(),
      ),
    );
    service.registerLazySingleton<HoldingPortfolioRepository>(
      () => HoldingPortfolioRepositoryImpl(holdingPortfolioService: service()),
    );

    service.registerLazySingleton<TradingNavigator>(
      () => TradingNavigatorImpl(),
    );

    service.registerFactory<DerivativeRepository>(
      () => DerivativeRepositoryImpl(
        restClient: service(),
        derivativeService: service(),
      ),
    );
  }

  @override
  List<RouteBase> router() {
    return tradingRouter();
  }

  @override
  List<LocalizationsDelegate> localizationsDelegates() {
    return [MultiLocalizationsDelegate.delegate];
  }

  @override
  String modulePath() {
    return 'vpTrading';
  }
}

class MultiLocalizationsDelegate extends AppLocalizationDelegate {
  const MultiLocalizationsDelegate();

  static const AppLocalizationDelegate delegate = MultiLocalizationsDelegate();

  @override
  Future<VPTradingLocalize> load(Locale locale) {
    return MultipleLocalizations.load(
      initializeMessages,
      locale,
      (l) => VPTradingLocalize.load(locale),
      setDefaultLocale: true,
    );
  }
}
