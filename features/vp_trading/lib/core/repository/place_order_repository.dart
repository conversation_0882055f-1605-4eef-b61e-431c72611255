import 'package:vp_common/error/transform_error.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_common/widget/log_api/models/app_base_response.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/constant/place_order_path_api.dart';
import 'package:vp_trading/core/service/place_order_service.dart';
import 'package:vp_trading/model/available_trade/available_trade_model.dart';
import 'package:vp_trading/model/derivative/users_config_model.dart';
import 'package:vp_trading/model/market_status/market_status_model.dart';
import 'package:vp_trading/model/order/gtc_old/check_model.dart';
import 'package:vp_trading/model/order/gtc_old/gtc_request.dart';
import 'package:vp_trading/model/order/gtc_old/pre_check_order_request.dart';
import 'package:vp_trading/model/order/gtc_old/request_id_params.dart';
import 'package:vp_trading/model/order/gtc_old/response/base_response.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/model/order/request/order_request_model.dart';

abstract class PlaceOrderRepository {
  Future<BaseResponse<AvailableTradeModel>> getAvailableTrade(
    String accountId,
    String symbol,
    String? quotePrice,
  );

  Future<BaseResponse<AvailableTradeModel>> tradingOrder(
    OrderRequestModel request,
  );

  Future<BaseResponse<AvailableTradeModel>> tradingOrderBroker(
    OrderRequestModel request,
  );

  Future<BaseResponse> tradingConditionOrder(
    ConditionOrderRequestModel request,
  );

  Future<BaseResponse> tradingDerivativeConditionOrder(
    ConditionOrderRequestModel request,
  );

  Future<BaseResponse<List<MarketStatusModel>>> getMarketStatus();

  Future<BaseResponse<UsersConfigModel>> getFuUserConfigs();

  Future<BaseResponse<UsersConfigModel>> updateUserConfig(
    UsersConfigModel userConfigModel,
  );

  /// GTC old
  Future<AppBaseResponse> preCheckOrder(
    String accountId,
    RequestIdParams params,
    PreCheckOrderRequest request,
  );

  Future<CheckModel> initVerifyTransaction(String accountId);

  Future conditionOrder(
    String accountId,
    RequestIdParams params,
    GtcRequest request,
  );
}

class PlaceOrderRepositoryImpl implements PlaceOrderRepository {
  final PlaceOrderService placeOrderService;
  final Dio _restClient;

  PlaceOrderRepositoryImpl({
    required Dio restClient,
    required this.placeOrderService,
  }) : _restClient = restClient;

  @override
  Future<BaseResponse<AvailableTradeModel>> getAvailableTrade(
    String accountId,
    String symbol,
    String? quotePrice,
  ) async {
    try {
      var quotePriceInt =
          (quotePrice == "null" || quotePrice == null || quotePrice.isEmpty)
              ? null
              : quotePrice;
      Map<String, dynamic> mapQueries = {};
      if (quotePriceInt == null || quotePriceInt.isEmpty) {
        mapQueries = {'symbol': symbol};
      } else {
        mapQueries = {'symbol': symbol, 'quotePrice': quotePriceInt};
      }
      final response = await placeOrderService.getAvailableTrade(
        accountId,
        mapQueries,
      );

      return response;
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse<AvailableTradeModel>> tradingOrder(
    OrderRequestModel request,
  ) async {
    try {
      final response = await placeOrderService.tradingOrder(request);

      return response;
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse> tradingConditionOrder(
    ConditionOrderRequestModel request,
  ) async {
    try {
      final response = await placeOrderService.tradingConditionOrder(request);

      return response;
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse> tradingDerivativeConditionOrder(
    ConditionOrderRequestModel request,
  ) async {
    try {
      final response = await placeOrderService.tradingDerivativeConditionOrder(
        request,
      );

      return response;
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse<List<MarketStatusModel>>> getMarketStatus() async {
    try {
      final response = await placeOrderService.getMarketStatus();

      return response;
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse<UsersConfigModel>> getFuUserConfigs() async {
    try {
      final response = await placeOrderService.getFuUserConfigs();

      return response;
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse<UsersConfigModel>> updateUserConfig(
    UsersConfigModel userConfigModel,
  ) async {
    try {
      final response = await placeOrderService.updateUserConfig(
        userConfigModel,
      );

      return response;
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<BaseResponse<AvailableTradeModel>> tradingOrderBroker(
    OrderRequestModel request,
  ) async {
    try {
      final response = await placeOrderService.tradingOrderBroker(request);

      return response;
    } catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<AppBaseResponse> preCheckOrder(
    String accountId,
    RequestIdParams params,
    PreCheckOrderRequest request,
  ) async {
    try {
      Response response = await _restClient.post(
        PlaceOrderPathApi.preCheckOrder(accountId),
        queryParameters: params.toJson(),
        data: request.toJson(),
      );
      return AppBaseResponse.fromJson(response.data);
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future<CheckModel> initVerifyTransaction(String accountId) async {
    try {
      Response response = await _restClient.post(
        PlaceOrderPathApi.initVerifyTransaction(accountId),
      );
      final result = AppBaseResponse.fromJson(response.data);
      if (result.isSuccess() && result.data != null) {
        return CheckModel.fromJson(result.data);
      } else {
        final errorResponse = ResponseError.fromDioResponse(response);
        throw errorResponse;
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
  }

  @override
  Future conditionOrder(
    String accountId,
    RequestIdParams params,
    GtcRequest request,
  ) async {
    try {
      Response response = await _restClient.post(
        PlaceOrderPathApi.conditionOrder(accountId),
        queryParameters: params.toJson(),
        data: request.toJson(),
      );

      final beResponse = BEBaseResponse.fromJson(response.data);

      if (beResponse.isSuccess()) {
        return beResponse;
      } else {
        throw ResponseError.fromDioResponse(response);
      }
    } on DioException catch (e) {
      throw HandleError.from(e);
    }
  }
}
