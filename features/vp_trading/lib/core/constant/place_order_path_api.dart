class PlaceOrderPathApi {
  static const getOLStockInfo = '/api/oddLotStockInfo';

  /*-----select stock-----*/
  // L<PERSON>y thông tin các mã chứng khoán nắm giữ của tài khoản
  static const securities = '/inq/securities';

  // Lấy các mã chứng khoán
  static const quotes = '/quotes';
  static const instruments = '/datafeed/instruments';

  /*-----order-----*/
  // Lấy thông tin sức mua của tiểu khoản
  static const availableTrade =
      '/neo-inv-customer/public/v1/accounts/availableTrade?accountId={accountId}';

  static const tradingOrder = '/neo-inv-order/public/v1/trading/orders';

  static const tradingConditionOrder =
      '/neo-inv-order/public/v1/trading/stockConditionOrder';

  static const tradingDerivativeConditionOrder =
      '/neo-inv-order/public/v1/trading/fuConditionOrder';

  static const tradingMarketStatus =
      '/neo-inv-tools/public/v1/market/marketStatus?marketCode=ALL';

  // Lấy thông tin cấu hình bước giá Phái Sinh
  static const fuUserConfigs = '/neo-inv-order/public/v1/trading/fuUserConfigs';

  // Tạo verify cho transaction đặt lệnh
  static initVerifyTransaction(String accountId) =>
      '/flex/accounts/$accountId/initVerifyTransaction';

  // preCheck Đặt lệnh
  static preCheckOrder(String accountId) =>
      '/flex/accounts/$accountId/precheckOrder';

  // Kiểm tra mã xác thực cho phần Order
  static const twoFactorAuth = '/twoFactorAuth';

  // Đặt lệnh
  static orders(String accountId) => '/accounts/$accountId/orders';

  // Đặt lệnh điều kiện
  static conditionOrder(String accountId) =>
      '/flex/accounts/$accountId/conditionOrder';

  // Đặt lệnh sale support
  static const String orderRecommendation = '/customer/stocks/order';

  // Đặt lệnh điều kiện
  static const String conditionalOrderRequest =
      '/condition-order/api/stoploss/newConditionOrderRequest';

  // Cảnh báo thông tin trước giao dịch của NNB, NLQ
  static const String shareHolder =
      '/flex-connector-external-api/external/v1/holders';

  // Bảng giá phiên Buy-in
  static const String multiBoardStockInfoByList =
      '/invest/api/v2/multiBoardStockInfoByList';

  // Bảng giá phiên Buy-in
  static const String stockInfoByListMultipleBoard =
      '/invest/api/v2/stockInfoByListMultipleBoard';

  static const String tradingOrderBroker =
      '/neo-inv-order/public/v1/trading/brokerRecommendationOrder';

  static const String fuConditionOrderBook =
      "/neo-inv-order/public/v1/trading/fuConditionOrderBook";
}
