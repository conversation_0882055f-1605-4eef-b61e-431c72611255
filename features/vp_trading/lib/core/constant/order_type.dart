import 'package:vp_trading/generated/l10n.dart';

enum OrderType {
  lo,
  buyIn,
  stopLoss,
  takeProfit,
  waiting,
  gtc;

  bool get isLo => this == OrderType.lo;

  bool get isBuyIn => this == OrderType.buyIn;

  bool get isStopLoss => this == OrderType.stopLoss;

  bool get isTakeProfit => this == OrderType.takeProfit;

  bool get isWaiting => this == OrderType.waiting;

  bool get isGtc => this == OrderType.gtc;

  bool get isLoOrGtc => isLo || isGtc;

  bool get isLoOrBuyIn => isLo || isBuyIn;

  bool get isCondition => isStopLoss || isTakeProfit || isWaiting;

  // OrderHistoryType get historyType =>
  //     isGtc ? OrderHistoryType.gtc : OrderHistoryType.normal;
}

extension OrderTypeExtension on OrderType {
  String get toName {
    switch (this) {
      case OrderType.lo:
        return VPTradingLocalize.current.trading_order_type_lo;
      case OrderType.buyIn:
        return VPTradingLocalize.current.trading_order_type_buyin;
      case OrderType.stopLoss:
        return VPTradingLocalize.current.trading_stop_loss;
      case OrderType.takeProfit:
        return VPTradingLocalize.current.trading_take_profit;
      case OrderType.waiting:
        return VPTradingLocalize.current.trading_waiting_command;
      case OrderType.gtc:
        return VPTradingLocalize.current.trading_order_type_gtc;
    }
  }

  String get toServer {
    switch (this) {
      case OrderType.lo:
        return '';
      case OrderType.buyIn:
        return '';
      case OrderType.stopLoss:
        return 'SLO';
      case OrderType.takeProfit:
        return 'TPO';
      case OrderType.waiting:
        return 'SEO';
      case OrderType.gtc:
        return 'GTC';
    }
  }
}

enum SessionType {
  ato,
  mp,
  mak,
  mok,
  mtl,
  atc,
  plo;

  bool get isAtoAtc => this == ato;

  static SessionType? parseString(String text) {
    for (var e in values) {
      if (text.toUpperCase() == e.name.toUpperCase()) {
        return e;
      }
    }
    return null;
  }
}

enum OrderChannelEnum { customer, broker, counter }

extension OrderChannelEnumExtension on OrderChannelEnum {
  String get value {
    switch (this) {
      case OrderChannelEnum.customer:
        return 'CUSTOMER';
      case OrderChannelEnum.broker:
        return 'BROKER';
      case OrderChannelEnum.counter:
        return 'COUNTER';
    }
  }
}

enum DerivativeOrderOptionalType {
  regular,
  stopOrder,
  trailingStop,
  stopLossOrTakeProfit,
}

extension DerivativeOrderOptionalTypeExtension on DerivativeOrderOptionalType {
  String get toName {
    switch (this) {
      case DerivativeOrderOptionalType.regular:
        return VPTradingLocalize.current.derivative_regular_order;
      case DerivativeOrderOptionalType.stopOrder:
        return VPTradingLocalize.current.derivative_stop_order_command;
      case DerivativeOrderOptionalType.trailingStop:
        return VPTradingLocalize.current.derivative_trailing_stop_command;
      case DerivativeOrderOptionalType.stopLossOrTakeProfit:
        return VPTradingLocalize
            .current
            .derivative_stop_loss_or_take_profit_command;
    }
  }

  bool get isStopOrder => this == DerivativeOrderOptionalType.stopOrder;

  bool get isRegular => this == DerivativeOrderOptionalType.regular;

  bool get isTrailingStop => this == DerivativeOrderOptionalType.trailingStop;

  bool get isStopLossOrTakeProfit =>
      this == DerivativeOrderOptionalType.stopLossOrTakeProfit;

  String get toServer {
    switch (this) {
      case DerivativeOrderOptionalType.regular:
        return '';
      case DerivativeOrderOptionalType.stopOrder:
        return 'STO';
      case DerivativeOrderOptionalType.trailingStop:
        return 'TSO';
      case DerivativeOrderOptionalType.stopLossOrTakeProfit:
        return 'BB';
    }
  }
}
