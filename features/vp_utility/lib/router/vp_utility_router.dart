enum VpUtilityRouter {
  codePilotGetToken('/code_pilot_get_token'),
  recommendationList('/recommendationList'),
  securitiesStatement('/securitiesStatement'),
  profitHistory('/profitHistory'),
  derivativeStatement('/derivativeStatement'),
  stockFilter('/stockFilter'),
  searchStock('/searchStock'),
  listEmonie('/listEmonie'),
  stockAlert('/stockAlert'),
  event('/event'),
  stockRight('/stockRight'),
  stockTransfer('/stockTransfer'),
  ranking('/ranking'),
  stockDetail('/noauth-stockDetail'),
  recommendation('/recommendationList'),
  modelPortfolio('/modelPortfolio'),
  advancePayment('/advancePayment'),
  advancePaymentSuccess('/advance_payment_success'),
  registerAdvancePayment('/registerAdvancePayment'),
  financialServicePackage('/financialServicePackage'),
  orderConfirm('/orderConfirm'),
  modelPortfolioContract('/modelPortfolioContract'),
  marketRouter('/market'),
  registerPackage('/registerPackage'),
  registerRight('/register_right'),
  registerEmonie('/registerEmonie'),
  statusEmonie('/statusEmonie'),
  tooltipEmonie('/tooltipEmonie'),
  detailsEmonie('/detailsEmonie'),
  derivativeIncomeStatement('/derivativeIncomeStatement'),
  createStockFilter('/create_stock_filter'),
  derivativeTransaction('/derivativeTransaction'),
  derivativeTransactionSuccess('/transactionSuccess'),
  derivativeTransactionFail('/transactionFail'),
  derivativeWithdrawSuccess('/withdrawSuccess'),
  derivativeWithdrawFail('/withdrawFail'),
  derivativeTransactionHistory('/derivativeTransactionHistory');

  final String routeName;

  const VpUtilityRouter(this.routeName);
}
