import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_utility/generated/assets.gen.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/router/vp_utility_router.dart';

import 'utilities_item_widget.dart';

class UtilitiesPage extends StatelessWidget {
  const UtilitiesPage({super.key});

  @override
  Widget build(BuildContext context) {
    final List<UtilsItem> utils = [
      UtilsItem(
        asset: VpUtilityAssets.icons.icSec.path,
        label: VpUtilityLocalize.current.utility_util_market_analytic,
        onTap: () => context.push(VpUtilityRouter.marketRouter.routeName),
      ),
      UtilsItem(
        asset: VpUtilityAssets.icons.icAi.path,
        label: "StockGuru",
        onTap: () => context.push(VpUtilityRouter.codePilotGetToken.routeName),
      ),
      UtilsItem(
        asset: VpUtilityAssets.icons.checkedbox.path,
        label: VpUtilityLocalize.current.utility_order_confirm,
        onTap: () => context.push(VpUtilityRouter.orderConfirm.routeName),
      ),
      UtilsItem(
        asset: VpUtilityAssets.icons.clipboardalt.path,
        label: VpUtilityLocalize.current.utility_financial_service_package,
        onTap:
            () =>
                context.push(VpUtilityRouter.financialServicePackage.routeName),
      ),
      UtilsItem(
        asset: VpUtilityAssets.icons.money.path,
        label: VpUtilityLocalize.current.utility_advance_payment,
        onTap: () => context.push(VpUtilityRouter.advancePayment.routeName),
      ),
      UtilsItem(
        asset: VpUtilityAssets.icons.icAsset.path,
        label: VpUtilityLocalize.current.utility_model_portfolio,
        onTap: () => context.push(VpUtilityRouter.modelPortfolio.routeName),
      ),
      UtilsItem(
        asset: VpUtilityAssets.icons.icFileUser.path,
        label:
            VpUtilityLocalize.current.utility_recommendation_invRecommendation,
        onTap: () => context.push(VpUtilityRouter.recommendation.routeName),
      ),
      UtilsItem(
        asset: VpUtilityAssets.icons.icRanking.path,
        label: VpUtilityLocalize.current.utility_ranking_industry_stock_ranking,
        onTap: () => context.push(VpUtilityRouter.ranking.routeName),
      ),
      UtilsItem(
        asset: VpUtilityAssets.icons.icTransferMoney.path,
        label: VpUtilityLocalize.current.utility_util_stock_transfer,
        onTap: () {
          context.push(VpUtilityRouter.stockTransfer.routeName).then((value) {
            if (value is String) {
              showMessage(isSuccess: true, value);
            }
          });
        },
      ),
      UtilsItem(
        asset: VpUtilityAssets.icons.edit.path,
        label: VpUtilityLocalize.current.stockRight,
        onTap: () => context.push(VpUtilityRouter.stockRight.routeName),
      ),
      UtilsItem(
        asset: VpUtilityAssets.icons.icEvent.path,
        label: VpUtilityLocalize.current.utility_event_calendar,
        onTap: () => context.push(VpUtilityRouter.event.routeName),
      ),
      UtilsItem(
        asset: VpUtilityAssets.icons.icStockAleart.path,
        label: VpUtilityLocalize.current.utility_utils_stock_alert,
        onTap: () => context.push(VpUtilityRouter.stockAlert.routeName),
      ),
      UtilsItem(
        asset: VpUtilityAssets.icons.icMoneyBag.path,
        label: VpUtilityLocalize.current.utility_eMonie,
        onTap: () => context.push(VpUtilityRouter.listEmonie.routeName),
      ),
    ];
    final filterStock = UtilsItem(
      asset: VpUtilityAssets.icons.filterStock.path,
      label: VpUtilityLocalize.current.utility_stockFilter,
      description: VpUtilityLocalize.current.utility_filter_description,
      onTap: () {},
    );
    return Scaffold(
      appBar: VPAppBar.layer(
        title: VpUtilityLocalize.current.utility_utilities,
        actions: [
          IconButton(
            icon: SvgPicture.asset(
              VpUtilityAssets.icons.search.path,
              package: VpUtilityAssets.package,
              colorFilter: ColorFilter.mode(themeData.primary, BlendMode.srcIn),
            ),
            onPressed: () {
              var args = SearchArgs(
                marketCodes: [
                  MarketCode.HOSE,
                  MarketCode.HNX,
                  MarketCode.UPCOM,
                  MarketCode.FU,
                ],
                itemAction: SearchItemAction.openDetail,
              );
              context.pushNamed(
                VPStockCommonRouter.search.routeName,
                queryParameters: args.toQueryParams(),
              );
            },
          ),
        ],
      ),
      body: ListView(
        children: [
          SizedBox(height: 16),
          _FilterStock(util: filterStock),
          _UtilGrid(utils: utils),
        ],
      ),
    );
  }
}

class _FilterStock extends StatelessWidget {
  final UtilsItem util;

  const _FilterStock({required this.util});

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: themeData.bgUtilities,
      ),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      height: UtilsItem.itemHeight,
      child: InkWell(
        onTap: () => context.push(VpUtilityRouter.stockFilter.routeName),
        child: ListTile(
          leading: SvgPicture.asset(
            util.asset,
            package: VpUtilityAssets.package,
            width: 40,
          ),
          title: Text(
            util.label,
            style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
          ),
          subtitle:
              util.description == null
                  ? null
                  : Text(
                    util.description!,
                    style: vpTextStyle.captionMedium.copyColor(
                      themeData.gray700,
                    ),
                  ),
        ),
      ),
    );
  }
}

class _UtilGrid extends StatelessWidget {
  final List<UtilsItem> utils;

  const _UtilGrid({required this.utils});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8).copyWith(top: 8),
      child: GridView.builder(
        physics: const ClampingScrollPhysics(),
        shrinkWrap: true,
        itemCount: utils.length,
        gridDelegate:
            const SliverGridDelegateWithFixedCrossAxisCountAndFixedHeight(
              crossAxisCount: 2,
              height: UtilsItem.itemHeight,
            ),
        itemBuilder: (_, index) => UtilitiesItemWidget(item: utils[index]),
      ),
    );
  }
}

class SliverGridDelegateWithFixedCrossAxisCountAndFixedHeight
    extends SliverGridDelegate {
  /// Creates a delegate that makes grid layouts with a fixed number of tiles in
  /// the cross axis.
  ///
  /// All of the arguments must not be null. The `mainAxisSpacing` and
  /// `crossAxisSpacing` arguments must not be negative. The `crossAxisCount`
  /// and `childAspectRatio` arguments must be greater than zero.
  const SliverGridDelegateWithFixedCrossAxisCountAndFixedHeight({
    required this.crossAxisCount,
    this.mainAxisSpacing = 0.0,
    this.crossAxisSpacing = 0.0,
    this.height = 56.0,
  }) : assert(crossAxisCount > 0),
       assert(mainAxisSpacing >= 0),
       assert(crossAxisSpacing >= 0),
       assert(height > 0);

  /// The number of children in the cross axis.
  final int crossAxisCount;

  /// The number of logical pixels between each child along the main axis.
  final double mainAxisSpacing;

  /// The number of logical pixels between each child along the cross axis.
  final double crossAxisSpacing;

  /// The height of the crossAxis.
  final double height;

  bool _debugAssertIsValid() {
    assert(crossAxisCount > 0);
    assert(mainAxisSpacing >= 0.0);
    assert(crossAxisSpacing >= 0.0);
    assert(height > 0.0);
    return true;
  }

  @override
  SliverGridLayout getLayout(SliverConstraints constraints) {
    assert(_debugAssertIsValid());
    final double usableCrossAxisExtent =
        constraints.crossAxisExtent - crossAxisSpacing * (crossAxisCount - 1);
    final double childCrossAxisExtent = usableCrossAxisExtent / crossAxisCount;
    final double childMainAxisExtent = height;
    return SliverGridRegularTileLayout(
      crossAxisCount: crossAxisCount,
      mainAxisStride: childMainAxisExtent + mainAxisSpacing,
      crossAxisStride: childCrossAxisExtent + crossAxisSpacing,
      childMainAxisExtent: childMainAxisExtent,
      childCrossAxisExtent: childCrossAxisExtent,
      reverseCrossAxis: axisDirectionIsReversed(constraints.crossAxisDirection),
    );
  }

  @override
  bool shouldRelayout(
    SliverGridDelegateWithFixedCrossAxisCountAndFixedHeight oldDelegate,
  ) {
    return oldDelegate.crossAxisCount != crossAxisCount ||
        oldDelegate.mainAxisSpacing != mainAxisSpacing ||
        oldDelegate.crossAxisSpacing != crossAxisSpacing ||
        oldDelegate.height != height;
  }
}
