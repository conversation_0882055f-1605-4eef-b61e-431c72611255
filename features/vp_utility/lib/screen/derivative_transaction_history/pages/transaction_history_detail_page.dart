import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/screen/derivative_transaction_history/utils/transaction_history_utils.dart';

import '../data/entities/transaction_history_entities.dart';
import '../data/enums/enum.dart';

class TransactionHistoryDetailPage extends StatefulWidget {
  const TransactionHistoryDetailPage({
    super.key,
    required this.transactionHistory,
  });

  final TransactionHistoryEntity transactionHistory;

  @override
  State<TransactionHistoryDetailPage> createState() =>
      _TransactionHistoryDetailPageState();
}

class _TransactionHistoryDetailPageState
    extends State<TransactionHistoryDetailPage> {
  late TransactionHistoryEntity item;

  @override
  void initState() {
    super.initState();
    item = widget.transactionHistory;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ItemRow(
          title: VpUtilityLocalize.current.subAccount,
          content: VpUtilityLocalize.current.derivative,
          transactionHistory: widget.transactionHistory,
          showStatus: false,
        ),
        ItemRow(
          title: VpUtilityLocalize.current.contractCodeDetail,
          content: item.symbol,
          transactionHistory: widget.transactionHistory,
        ),
        ItemRow(
          title: VpUtilityLocalize.current.command,
          // content: 'LONG',
          transactionHistory: widget.transactionHistory,
          showLongOrShort: true,
        ),
        ItemRow(
          title: VpUtilityLocalize.current.commandType,
          content: item.priceType,
          transactionHistory: widget.transactionHistory,
        ),
        ItemRow(
          title: VpUtilityLocalize.current.orderNumber,
          content: item.orderId,
          transactionHistory: widget.transactionHistory,
        ),
        ItemRow(
          title: VpUtilityLocalize.current.priceOrder,
          content: TransactionHistoryUtils().getPriceOrPriceType(
            price: item.price,
            priceType: item.priceType,
          ),
          transactionHistory: widget.transactionHistory,
        ),
        ItemRow(
          title: VpUtilityLocalize.current.amountOrder2,
          content: item.qty.toString(),
          transactionHistory: widget.transactionHistory,
        ),
        ItemRow(
          title: VpUtilityLocalize.current.orderTime2,
          content: item.tradeTime?.formatToddMMyyyyHHmm(),
          transactionHistory: widget.transactionHistory,
        ),
        ItemRow(
          title: VpUtilityLocalize.current.orderChannel2,
          content: getDataTypeFromString(item.via).description,
          transactionHistory: widget.transactionHistory,
        ),
        const SizedBox(height: 16),
        Divider(thickness: 1, height: 1, color: themeData.divider),
        const SizedBox(height: 24),
        ItemRow(
          title: VpUtilityLocalize.current.matchedVolume,
          content:
              '${TransactionHistoryUtils().formatNumber(item.execQty)}/${TransactionHistoryUtils().formatNumber(item.qty)}',
          transactionHistory: widget.transactionHistory,
        ),
        ItemRow(
          title: VpUtilityLocalize.current.averageMatchPrice,
          content: TransactionHistoryUtils().formatPrice(item.execPrice),
          transactionHistory: widget.transactionHistory,
        ),
        ItemRow(
          title: VpUtilityLocalize.current.status,
          content: '',
          transactionHistory: widget.transactionHistory,
          showStatus: true,
        ),
        ItemRow(
          title: VpUtilityLocalize.current.noteSecond,
          content: TransactionHistoryUtils().getNote(
            isDisposal: item.isForced,
            isChildCondOrder: item.isChildOrder,
            tranHisModel: item,
          ),
          transactionHistory: widget.transactionHistory,
        ),
        const SizedBox(height: 24),
        InkWell(
          onTap: () {
            context.pop();
          },
          child: Container(
            width: double.infinity,
            height: 40,
            decoration: BoxDecoration(
              border: Border.all(color: themeData.gray700, width: 1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Text(
                  VpUtilityLocalize.current.close,
                  style: vpTextStyle.subtitle16?.copyColor(vpColor.textPrimary),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class ItemRow extends StatelessWidget {
  const ItemRow({
    super.key,
    this.title,
    this.content,
    this.titleColor,
    this.contentColor,
    this.showStatus,
    this.showLongOrShort,
    required this.transactionHistory,
  });

  final String? title;
  final String? content;
  final Color? titleColor;
  final Color? contentColor;
  final bool? showStatus;
  final bool? showLongOrShort;

  final TransactionHistoryEntity transactionHistory;

  @override
  Widget build(BuildContext context) {
    final orderStatus = transactionHistory.orderStatus;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _TitleText(title: title),
          if (showStatus == true) _StatusBox(orderStatus: orderStatus),
          if (showLongOrShort == true)
            _LongOrShortText(execType: transactionHistory.execType),
          if (showStatus != true && showLongOrShort != true)
            Flexible(child: _ContentText(content: content)),
        ],
      ),
    );
  }
}

class _TitleText extends StatelessWidget {
  final String? title;

  const _TitleText({this.title});

  @override
  Widget build(BuildContext context) {
    return Text(
      title ?? '',
      style: vpTextStyle.body14?.copyWith(
        fontSize: 14,
        color: themeData.gray700,
      ),
    );
  }
}

class _ContentText extends StatelessWidget {
  final String? content;

  const _ContentText({this.content});

  @override
  Widget build(BuildContext context) {
    return Text(
      textAlign: TextAlign.end,
      content ?? '',
      style: vpTextStyle.body14?.copyWith(
        fontSize: 14,
        color: vpColor.textPrimary,
      ),
    );
  }
}

class _StatusBox extends StatelessWidget {
  final DerivativeOrderStatus? orderStatus;

  const _StatusBox({this.orderStatus});

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.centerRight,
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: orderStatus?.color,
          borderRadius: const BorderRadius.all(Radius.circular(4.0)),
        ),
        child: Padding(
          padding: const EdgeInsets.only(left: 4, right: 4, bottom: 6, top: 4),
          child: Text(
            orderStatus?.statusText ?? '',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: vpColor.textPrimary,
            ),
          ),
        ),
      ),
    );
  }
}

class _LongOrShortText extends StatelessWidget {
  final String? execType;

  const _LongOrShortText({required this.execType});

  @override
  Widget build(BuildContext context) {
    return Text(
      _getDisplayText(execType ?? ''),
      style: vpTextStyle.body14?.copyWith(color: _getTextColor(execType ?? '')),
    );
  }

  String _getDisplayText(String execType) {
    if (execType == "NB") {
      return "LONG";
    } else if (execType == "NS") {
      return "SHORT";
    } else {
      return "--";
    }
  }

  Color _getTextColor(String execType) {
    if (execType == "NB") {
      return themeData.primaryDark;
    } else if (execType == "NS") {
      return themeData.red;
    } else {
      return themeData.white;
    }
  }
}
