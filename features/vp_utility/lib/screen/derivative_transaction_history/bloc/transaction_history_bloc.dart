import 'dart:async';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_utility/core/base/base_decoder.dart';
import 'package:vp_utility/screen/derivative_transaction_history/data/repository/transaction_history_repository_impl.dart';

import '../data/adapter/transaction_history_adapter.dart';
import '../data/entities/transaction_history_entities.dart';
import '../data/models/transaction_history_model.dart';
import '../data/params/transaction_history_params.dart';
import '../widget/transaction_history_value_constans.dart';

part 'transaction_history_state.dart';

class TransactionHistoryBloc extends Cubit<TransactionHistoryState> {
  TransactionHistoryBloc({required this.subAccountModel})
    : super(TransactionHistoryState());

  final DerivativeTransactionHistoryRepository _repository =
      GetIt.instance.get();

  final SubAccountModel subAccountModel;

  final TextEditingController textSearchTransactionHistoryController =
      TextEditingController();

  int _pageIndex = 1;
  final int _pageSize = 20;
  int _currentLen = 0;
  bool hasLoadMore = true;
  String fromDate = '';
  String toDate = '';
  String? symbol;
  String orderType = 'ALL';
  String status = 'ALL';

  Timer? _debounce;

  TransactionType selectedTransactionType = TransactionType.all;
  List<StatusesType> selectedStatuses = StatusesType.values;
  List<TransactionHistoryEntity> transHis = [];

  void changeDataToFilter({
    required TransactionType transactionType,
    required List<StatusesType> listStatusesType,
  }) {
    _pageIndex = 1;
    orderType = getCodeTransactionType(transactionType);
    final statusCodes = listStatusesType.expand((status) => status.code);
    status = statusCodes.isNotEmpty ? statusCodes.join(',') : 'ALL';

    emit(
      state.copyWith(
        selectedTransactionType: transactionType,
        selectedStatuses: listStatusesType,
        orderType: orderType,
        status: status,
      ),
    );
    initFilterDefault();
  }

  void initData() async {
    await getDayOfPreviousMonth();
    // initFilterDefault();
    changeDataToFilter(
      transactionType: selectedTransactionType,
      listStatusesType: selectedStatuses,
    );
  }

  void initFilterDefault() {
    onLoadData();
  }

  void onLoadData({isLoadMore = false}) async {
    await getListTransactionHistory(isLoadMore: isLoadMore);
  }

  Future<List<TransactionHistoryModel>?> getListTransactionHistory({
    isLoadMore = false,
  }) async {
    try {
      if (!isLoadMore) {
        emit(state.copyWith(isLoadingListTransactionHistory: true));
      }
      final param = _createTransactionHistoryParam();
      final result = await _repository.getListTransactionHistory(param);
      if (result.isSuccess) {
        return onGetListSuccess(result: result, isLoadMore: isLoadMore);
      } else {
        transHis.clear();
        emit(state.copyWith(isLoadingListTransactionHistory: false));
      }
    } catch (error) {
      emit(state.copyWith(isLoadingListTransactionHistory: false));
      showError(error);
    }
    return null;
  }

  Future<List<TransactionHistoryModel>> onGetListSuccess({
    required BaseDecoder<List<TransactionHistoryModel>> result,
    required bool isLoadMore,
  }) async {
    _currentLen = result.model.length;
    final entityList = _mapTransactionHistory(result.model);
    if (!isLoadMore) {
      transHis = [];
    }
    transHis.addAll(entityList);
    emit(
      state.copyWith(
        isLoadingListTransactionHistory: false,
        listTransactionHistory: transHis,
      ),
    );
    return result.model;
  }

  TransactionHistoryParam _createTransactionHistoryParam() {
    return TransactionHistoryParam(
      pageIndex: _pageIndex,
      pageSize: _pageSize,
      fromDate: fromDate,
      toDate: toDate,
      symbol: symbol,
      orderType: getTransactionTypeValue(state.selectedTransactionType!),
      status: status,
      accountId: subAccountModel.id!,
      productTypeCd: subAccountModel.productTypeCd!,
    );
  }

  List<TransactionHistoryEntity> _mapTransactionHistory(
    List<TransactionHistoryModel> modelList,
  ) {
    return List.generate(
      modelList.length,
      (index) => TransactionHistoryAdapter(modelList[index]).genderForUi(),
    );
  }

  Future onLoadMore() async {
    if (!hasLoadMore) {
      return;
    }
    if (_currentLen < _pageSize) {
      hasLoadMore = false;
    }
    _pageIndex++;
    onLoadData(isLoadMore: true);
  }

  Future onRefresh() async {
    _pageIndex = 1;
    hasLoadMore = true;
    transHis.clear();
    onLoadData();
  }

  Future<void> changeDate({
    required String startDate,
    required String endDate,
  }) async {
    fromDate = startDate;
    toDate = endDate;
    _pageIndex = 1;
    _currentLen = 0;
  }

  Future<void> onUpdateTimeFilter({
    required String startDate,
    required String endDate,
  }) async {
    await changeDate(startDate: startDate, endDate: endDate);
    onLoadData();
  }

  Future<void> onUpdateTimeFilterReset() async {
    await getDayOfPreviousMonth();
    onLoadData();
  }

  /// sau 0.3s thì search
  void onSearchChanged({required String value}) {
    symbol =
        textSearchTransactionHistoryController.text.isNullOrEmpty
            ? 'ALL'
            : textSearchTransactionHistoryController.text;
    emit(
      state.copyWith(textSearch: textSearchTransactionHistoryController.text),
    );
    if (_debounce?.isActive ?? false) _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 300), () {
      initFilterDefault();
    });
  }

  void clearTextSearch({required String text}) {
    emit(state.copyWith(textSearch: text));
    onSearchChanged(value: text);
  }

  void dispose() {
    _debounce?.cancel();
    textSearchTransactionHistoryController.dispose();
  }

  bool hasOrderId(List<TransactionHistoryEntity> orders, String orderId) {
    return orders.any((order) => order.orderId == orderId);
  }

  Future<void> getDayOfPreviousMonth() async {
    emit(state.copyWith(isLoadingListTransactionHistory: true));
    // Lấy ngày hiện tại
    DateTime now = DateTime.now();
    DateTime yesterday = now.subtract(const Duration(days: 1));

    // Hàm trừ 1 tháng
    DateTime subtractOneMonth(DateTime date) {
      // Nếu tháng là tháng 1, cần chuyển sang tháng 12 của năm trước
      int newYear = date.month == 1 ? date.year - 1 : date.year;
      int newMonth = date.month == 1 ? 12 : date.month - 1;

      // Lấy ngày cuối cùng của tháng trước
      int dayInNewMonth = DateTime(newYear, newMonth + 1, 0).day;

      // Nếu ngày hiện tại lớn hơn ngày cuối cùng của tháng trước, sử dụng ngày cuối cùng của tháng trước
      int newDay = date.day > dayInNewMonth ? dayInNewMonth : date.day;
      emit(state.copyWith(isLoadingListTransactionHistory: false));

      return DateTime(newYear, newMonth, newDay);
    }

    // Lấy ngày sau khi trừ 1 tháng
    DateTime lastMonth = subtractOneMonth(yesterday);

    // Định dạng ngày theo ý muốn (vd: yyyy-MM-dd)
    DateFormat dateFormat = DateFormat('dd/MM/yyyy');
    String formattedDateNow = dateFormat.format(yesterday);
    String formattedDateLastMonth = dateFormat.format(lastMonth);

    fromDate = formattedDateLastMonth;
    toDate = formattedDateNow;
  }
}
