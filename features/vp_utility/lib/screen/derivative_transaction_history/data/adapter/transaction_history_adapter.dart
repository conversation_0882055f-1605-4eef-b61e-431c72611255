
import 'package:vp_utility/screen/derivative_transaction_history/data/entities/transaction_history_entities.dart';
import 'package:vp_utility/screen/derivative_transaction_history/data/models/transaction_history_model.dart';

class TransactionHistoryAdapter {
  final TransactionHistoryModel model;

  TransactionHistoryAdapter(this.model);

  TransactionHistoryEntity genderForUi() {
    return TransactionHistoryEntity(
      symbol: model.symbol,
      tradeTime: model.tradeTime,
      execQty: model.execQty,
      qty: model.qty,
      execPrice: model.execPrice,
      execType: model.execType,
      price: model.price,
      orderStatus: model.orderStatus,
      priceType: model.priceType,
      isForced: model.isForced,
      isChildOrder: model.isChildOrder,
      orderId: model.orderId,
      via: model.via,
    );
  }
}
