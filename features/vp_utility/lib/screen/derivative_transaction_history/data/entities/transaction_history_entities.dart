import 'package:vp_utility/screen/derivative_transaction_history/data/enums/enum.dart';

class TransactionHistoryEntity {
  String? orderId;

  String? symbol;

  String? priceType;

  String? execType;

  DerivativeOrderStatus? orderStatus;

  num? qty;

  num? price;

  num? execQty;

  num? execPrice;

  String? via;

  DateTime? tradeTime;

  String? isForced;

  String? isChildOrder;

  String? quoteid;

  TransactionHistoryEntity({
    this.orderId,
    this.symbol,
    this.priceType,
    this.execType,
    this.orderStatus,
    this.qty,
    this.price,
    this.execQty,
    this.execPrice,
    this.via,
    this.tradeTime,
    this.isForced,
    this.isChildOrder,
    this.quoteid,
  });
}
