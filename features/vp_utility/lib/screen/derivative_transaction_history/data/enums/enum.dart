import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/l10n.dart';

enum DerivativeOrderStatus {
  PS,
  WT,
  WA,
  WC,
  WE,
  PR,
  PC,
  OP,
  WD,
  ST,
  PF,
  FF,
  CP,
  RP,
  CN,
  RJ,
  EX,
  EP,
}

extension OrderStatusExtension on DerivativeOrderStatus {
  String get statusText {
    switch (this) {
      case DerivativeOrderStatus.PS:
        return 'Chờ <PERSON>ửi';
      case DerivativeOrderStatus.WT:
      case DerivativeOrderStatus.WA:
      case DerivativeOrderStatus.WC:
      case DerivativeOrderStatus.WE:
      case DerivativeOrderStatus.PR:
      case DerivativeOrderStatus.PC:
      case DerivativeOrderStatus.OP:
      case DerivativeOrderStatus.WD:
        return 'Đang chờ';
      case DerivativeOrderStatus.ST:
        return 'Đã <PERSON>';
      case DerivativeOrderStatus.PF:
        return 'Khớp 1 phần';
      case DerivativeOrderStatus.FF:
      case DerivativeOrderStatus.CP:
        return 'Khớp hết';
      case DerivativeOrderStatus.RP:
        return 'Đã Sửa';
      case DerivativeOrderStatus.CN:
        return 'Đã Hủy';
      case DerivativeOrderStatus.RJ:
        return 'Từ Chối';
      case DerivativeOrderStatus.EX:
        return 'Hết hạn';
      case DerivativeOrderStatus.EP:
        return 'Hết hiệu lực';
    }
  }

  Color get color {
    switch (this) {
      case DerivativeOrderStatus.PS:
        return themeData.blue.withOpacity(0.16);
      case DerivativeOrderStatus.WT:
      case DerivativeOrderStatus.WA:
      case DerivativeOrderStatus.WC:
      case DerivativeOrderStatus.WE:
      case DerivativeOrderStatus.PR:
      case DerivativeOrderStatus.PC:
      case DerivativeOrderStatus.OP:
      case DerivativeOrderStatus.WD:
        return themeData.blue.withOpacity(0.16);
      case DerivativeOrderStatus.ST:
        return themeData.blue.withOpacity(0.16);
      case DerivativeOrderStatus.PF:
        return themeData.blue.withOpacity(0.16);
      case DerivativeOrderStatus.FF:
      case DerivativeOrderStatus.CP:
        return themeData.increaseColor.withOpacity(0.16);
      case DerivativeOrderStatus.RP:
        return themeData.increaseColor.withOpacity(0.16);
      case DerivativeOrderStatus.CN:
        return themeData.decreaseColor.withOpacity(0.16);
      case DerivativeOrderStatus.RJ:
        return themeData.decreaseColor.withOpacity(0.16);
      case DerivativeOrderStatus.EX:
        return themeData.decreaseColor.withOpacity(0.16);
      case DerivativeOrderStatus.EP:
        return themeData.decreaseColor.withOpacity(0.16);
    }
  }
}

enum DataType { F, A, O, T, M, H, Y, Z, C, P, R, E, W, B, K, L, unknown }

extension DataTypeExtension on DataType {
  String get description {
    switch (this) {
      case DataType.F:
        return "Tại sàn";
      case DataType.A:
        return "-";
      case DataType.O:
        return "Online";
      case DataType.T:
        return "Điện thoại";
      case DataType.M:
        return "Mobile";
      case DataType.H:
        return "Home Trading";
      case DataType.Y:
        return "Mobile";
      case DataType.Z:
        return "Online";
      case DataType.C:
        return "-";
      case DataType.P:
        return "CopyPTrade";
      case DataType.R:
        return "Tại sàn";
      case DataType.E:
        return "Lệnh điều kiện";
      case DataType.W:
        return "MM";
      case DataType.B:
        return "Bloomberg";
      case DataType.K:
        return "Tích sản";
      case DataType.L:
        return "Wealth";
      default:
        return "-";
    }
  }
}

DataType getDataTypeFromString(String? value) {
  switch (value?.toUpperCase()) {
    case 'F':
      return DataType.F;
    case 'A':
      return DataType.A;
    case 'O':
      return DataType.O;
    case 'T':
      return DataType.T;
    case 'M':
      return DataType.M;
    case 'H':
      return DataType.H;
    case 'Y':
      return DataType.Y;
    case 'Z':
      return DataType.Z;
    case 'C':
      return DataType.C;
    case 'P':
      return DataType.P;
    case 'R':
      return DataType.R;
    case 'E':
      return DataType.E;
    case 'W':
      return DataType.W;
    case 'B':
      return DataType.B;
    case 'K':
      return DataType.K;
    case 'L':
      return DataType.K;
    default:
      return DataType.unknown;
  }
}
