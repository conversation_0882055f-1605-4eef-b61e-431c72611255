import 'package:dio/dio.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_utility/core/base/base_decoder.dart';
import 'package:vp_utility/core/base/base_response/base_response.dart';
import 'package:vp_utility/screen/derivative_transaction_history/data/models/transaction_history_model.dart';
import 'package:vp_utility/screen/derivative_transaction_history/data/params/transaction_history_params.dart';
import 'package:vp_utility/screen/derivative_transaction_history/data/service/derivative_transaction_history_service.dart';

abstract class DerivativeTransactionHistoryRepository {
  Future<BaseDecoder<List<TransactionHistoryModel>>> getListTransactionHistory(
    TransactionHistoryParam param,
  );
}

class DerivativeTransactionHistoryRepositoryImpl
    implements DerivativeTransactionHistoryRepository {
  final DerivativeTransactionHistoryService service;

  DerivativeTransactionHistoryRepositoryImpl({required this.service});

  @override
  Future<BaseDecoder<List<TransactionHistoryModel>>> getListTransactionHistory(
    TransactionHistoryParam param,
  ) async {
    try {
      final response = await service.getListTransactionHistory(
        param.accountId,
        fromDate: param.fromDate,
        toDate: param.toDate,
        pageSize: param.pageSize,
        pageIndex: param.pageIndex,
        orderType: param.orderType,
        symbol: param.symbol,
        status: param.status,
        productTypeCd: param.productTypeCd,
      );

      return BaseDecoder(
        BaseResponseMeta.fromJson(response),
        decoder: transactionHistoryListFromJson,
      );
    } on DioException catch (e) {
      throw HandleError.from<CoreErrorResponse>(e);
    }
  }
}
