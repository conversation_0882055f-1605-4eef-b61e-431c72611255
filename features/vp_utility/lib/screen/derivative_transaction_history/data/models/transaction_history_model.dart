import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vp_utility/screen/derivative_transaction_history/data/enums/enum.dart';

part 'transaction_history_model.g.dart';

List<TransactionHistoryModel> transactionHistoryListFromJson(dynamic json) {
  final list = <TransactionHistoryModel>[];
  final array = json?['content'] ?? [];
  array?.map((v) {
    list.add(TransactionHistoryModel.fromJson(v));
  }).toList();
  return list;
}

@JsonSerializable()
class TransactionHistoryModel {
  final String? orderId;

  final String? symbol;

  final String? execType;

  final String? priceType;

  @JsonKey(unknownEnumValue: JsonKey.nullForUndefinedEnumValue)
  final DerivativeOrderStatus? orderStatus;

  final num? qty;

  final num? price;

  final num? execQty;

  final num? execPrice;

  final String? parentOrderId;

  final String? via;

  final DateTime? tradeTime;

  final String? isForced;

  final String? isChildOrder;

  const TransactionHistoryModel({
    this.orderId,
    this.symbol,
    this.execType,
    this.priceType,
    this.orderStatus,
    this.qty,
    this.price,
    this.execQty,
    this.execPrice,
    this.parentOrderId,
    this.via,
    this.tradeTime,
    this.isForced,
    this.isChildOrder,
  });

  factory TransactionHistoryModel.fromJson(Map<String, dynamic> json) =>
      _$TransactionHistoryModelFromJson(json);

  Map<String, dynamic> toJson() => _$TransactionHistoryModelToJson(this);
}
