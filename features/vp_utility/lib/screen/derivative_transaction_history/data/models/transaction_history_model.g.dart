// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_history_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TransactionHistoryModel _$TransactionHistoryModelFromJson(
  Map<String, dynamic> json,
) => TransactionHistoryModel(
  orderId: json['orderId'] as String?,
  symbol: json['symbol'] as String?,
  execType: json['execType'] as String?,
  priceType: json['priceType'] as String?,
  orderStatus: $enumDecodeNullable(
    _$DerivativeOrderStatusEnumMap,
    json['orderStatus'],
    unknownValue: JsonKey.nullForUndefinedEnumValue,
  ),
  qty: json['qty'] as num?,
  price: json['price'] as num?,
  execQty: json['execQty'] as num?,
  execPrice: json['execPrice'] as num?,
  parentOrderId: json['parentOrderId'] as String?,
  via: json['via'] as String?,
  tradeTime:
      json['tradeTime'] == null
          ? null
          : DateTime.parse(json['tradeTime'] as String),
  isForced: json['isForced'] as String?,
  isChildOrder: json['isChildOrder'] as String?,
);

Map<String, dynamic> _$TransactionHistoryModelToJson(
  TransactionHistoryModel instance,
) => <String, dynamic>{
  'orderId': instance.orderId,
  'symbol': instance.symbol,
  'execType': instance.execType,
  'priceType': instance.priceType,
  'orderStatus': _$DerivativeOrderStatusEnumMap[instance.orderStatus],
  'qty': instance.qty,
  'price': instance.price,
  'execQty': instance.execQty,
  'execPrice': instance.execPrice,
  'parentOrderId': instance.parentOrderId,
  'via': instance.via,
  'tradeTime': instance.tradeTime?.toIso8601String(),
  'isForced': instance.isForced,
  'isChildOrder': instance.isChildOrder,
};

const _$DerivativeOrderStatusEnumMap = {
  DerivativeOrderStatus.PS: 'PS',
  DerivativeOrderStatus.WT: 'WT',
  DerivativeOrderStatus.WA: 'WA',
  DerivativeOrderStatus.WC: 'WC',
  DerivativeOrderStatus.WE: 'WE',
  DerivativeOrderStatus.PR: 'PR',
  DerivativeOrderStatus.PC: 'PC',
  DerivativeOrderStatus.OP: 'OP',
  DerivativeOrderStatus.WD: 'WD',
  DerivativeOrderStatus.ST: 'ST',
  DerivativeOrderStatus.PF: 'PF',
  DerivativeOrderStatus.FF: 'FF',
  DerivativeOrderStatus.CP: 'CP',
  DerivativeOrderStatus.RP: 'RP',
  DerivativeOrderStatus.CN: 'CN',
  DerivativeOrderStatus.RJ: 'RJ',
  DerivativeOrderStatus.EX: 'EX',
  DerivativeOrderStatus.EP: 'EP',
};
