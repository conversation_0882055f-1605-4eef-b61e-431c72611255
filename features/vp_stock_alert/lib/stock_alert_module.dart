import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_common/vp_common.dart' hide AppLocalizationDelegate;
import 'package:vp_stock_alert/core/dio_manager.dart';
import 'package:vp_stock_alert/core/repository/stock_alert_repository.dart';
import 'package:vp_stock_alert/core/repository/stock_common_repo.dart';
import 'package:vp_stock_alert/core/repository/stock_common_repo_impl.dart';
import 'package:vp_stock_alert/core/service/stock_alert_service.dart';
import 'package:vp_stock_alert/generated/l10n.dart';
import 'package:vp_stock_alert/pages/stock_alert/stock_alert_page.dart';
import 'package:vp_stock_alert/router/stock_alert_router.dart';
import 'package:vp_stock_alert/stock_alert_navigator.dart';
import 'package:vp_stock_alert/generated/intl/messages_all.dart';

import 'pages/stock_alert/bloc/follow_list_bloc.dart';
import 'pages/stock_alert/bloc/holding_list_bloc.dart';
import 'pages/stock_alert/bloc/search_alert_stock_bloc.dart';
import 'pages/stock_alert/bloc/stock_alert/stock_alert_cubit.dart';

class VPStockAlertModule implements Module {
  @override
  void injectServices(GetIt service) {
    service.registerLazySingleton<DioManager>(() => DioManager.instance);

    service.registerLazySingleton<StockCommonRepo>(
      () => StockCommonRepoImpl(investDio: service<DioManager>().investDio),
    );
    service.registerLazySingleton(() => StockAlertService(service()));
    service.registerLazySingleton<StockAlertRepository>(
      () => StockAlertRepositoryImpl(service: service()),
    );

    service.registerLazySingleton<StockAlertNavigator>(
      () => StockAlertNavigatorImpl(),
    );
  }

  @override
  List<RouteBase> router() {
    return [
      GoRoute(
        path: VPStockAlertRouter.stockAlert.routeName,
        name: VPStockAlertRouter.stockAlert.routeName,
        builder: (context, state) {
          return MultiBlocProvider(
            providers: [
              BlocProvider(create: (_) => StockAlertCubit()),
              BlocProvider(create: (context) => SearchAlertStockBloc()),
              BlocProvider(create: (context) => HoldingListBloc()),
              BlocProvider(create: (context) => FollowListBloc()),
            ],
            child: const StockAlertPage(),
          );
        },
      ),
    ];
  }

  @override
  List<LocalizationsDelegate> localizationsDelegates() {
    return [MultiLocalizationsDelegate.delegate];
  }

  @override
  String modulePath() {
    return 'vpStockDetail';
  }
}

class MultiLocalizationsDelegate extends AppLocalizationDelegate {
  const MultiLocalizationsDelegate();

  static const AppLocalizationDelegate delegate = MultiLocalizationsDelegate();

  @override
  Future<VPStockAlertLocalize> load(Locale locale) {
    return MultipleLocalizations.load(
      initializeMessages,
      locale,
      (l) => VPStockAlertLocalize.load(locale),
      setDefaultLocale: true,
    );
  }
}
