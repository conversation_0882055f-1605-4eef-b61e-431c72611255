import 'package:dio/dio.dart';
import 'package:vp_core/base/http_services.dart';
import 'package:vp_core/vp_core.dart';

class DioManager {
  static DioManager? _instance;

  static DioManager get instance => _instance ??= DioManager._();

  DioManager._();

  Dio? _restDio;
  Dio? _investDio;
  Dio? _flexDio;
  Dio? _baseDio;
  Dio? _restSaleSupportDio;
  Dio? _noAuthDio;
  Dio? _restKrxDio;
  Dio? _emoneiDio;

  Dio get restDio => _restDio ??= _createDio('rest');

  Dio get investDio => _investDio ??= _createDio('invest');

  Dio get flexDio => _flexDio ??= _createDio('flex');

  Dio get baseDio => _baseDio ??= _createDio('baseDio');

  Dio get restSaleSupportDio =>
      _restSaleSupportDio ??= _createDio('restSaleSupportDio');

  Dio get noAuthDio => _noAuthDio ??= _createDio('noAuthDio');

  Dio get restKrxDio => _restKrxDio ??= _createDio('restKrxDio');

  Dio get emoneiDio => _emoneiDio ??= _createDio('emoneiDio');

  /// Create configured Dio instance
  Dio _createDio(String type) {
    final baseUrl = switch (type) {
      'rest' => 'https://external-uat-krx.vpbanks.com.vn',
      'invest' => 'https://external-uat-krx.vpbanks.com.vn/invest',
      'flex' => 'https://external-uat-krx.vpbanks.com.vn/flex',
      'baseDio' => 'https://external-uat-krx.vpbanks.com.vn',
      'restSaleSupportDio' =>
        'https://external-uat-krx.vpbanks.com.vn/sales-support',
      'noAuthDio' => 'https://external-uat-krx.vpbanks.com.vn/noauth',
      'restKrxDio' => 'https://external-uat-krx.vpbanks.com.vn/flex',
      'emoneiDio' => 'https://emonei-external-uat-krx.vpbanks.com.vn',
      _ => throw ArgumentError('Unknown type: $type'),
    };

    return setupDio(baseUrl);
  }
}
