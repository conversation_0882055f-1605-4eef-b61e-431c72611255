import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_money/core/repository/money_repository.dart';
import 'package:vp_money/features/money_cash_in/webview_page_v2.dart';
import 'package:vp_money/generated/l10n.dart';
import 'package:vp_money/model/response/money_cash_in/initial_cash_in_model.dart';
import 'package:vp_money/model/response/money_cash_in/money_bank.dart';
import 'package:vp_money/model/response/money_cash_in/money_vir_account.dart';

part 'money_cash_in_cubit.freezed.dart';
part 'money_cash_in_state.dart';

class MoneyCashInCubit extends Cubit<MoneyCashInState> {
  MoneyCashInCubit() : super(const MoneyCashInState());
  final MoneyRepository _repository = GetIt.instance<MoneyRepository>();
  Future<void> initData() async {
    final selectSubAccount =
        GetIt.instance<SubAccountCubit>().defaultSubAccount;
    emit(state.copyWith(selectSubAccount: selectSubAccount));
  }

  void updateListVirAccount({required List<MoneyVirAccount> listVirAccount}) {
    setSelectVirAccount(
      listVirAccount.firstWhere(
        (e) => e.productTypeCd == state.selectSubAccount?.productTypeCd,
        orElse: () => listVirAccount.firstOrNull!,
      ),
    );
    emit(state.copyWith(listVirAccount: listVirAccount));
  }

  void updateListBank({
    required List<MoneyBank> listBankActive,
    required List<MoneyBank> listBankInActive,
  }) {
    emit(
      state.copyWith(
        listBank: listBankInActive,
        listBankActive: listBankActive,
        isLoading: false,
      ),
    );
    if (listBankActive.firstOrNull != null) {
      setTransferAccount(listBankActive.first);
    }
  }

  void setSelectVirAccount(MoneyVirAccount? account) {
    emit(state.copyWith(selectVirAccount: account));
  }

  void setSubAccount(SubAccountModel obj) {
    setSelectVirAccount(
      state.listVirAccount.firstWhere(
        (e) => e.productTypeCd == obj.productTypeCd,
        orElse: () => state.listVirAccount.firstOrNull!,
      ),
    );
    emit(state.copyWith(selectSubAccount: obj));
  }

  Future<void> setTransferAccount(MoneyBank bank) async {
    emit(state.copyWith(selectBank: bank));
    if (bank.getPartnerCodeStatus == PartnerCodeStatus.VPB) {
      await getAccountBalance();
    }
  }

  Future<void> checkLinkRelationWithVPBank() async {
    try {
      emit(state.copyWith(isLoading: true));
      final value = await _repository.getCheckLinkRelationWithVPBank();
      if (value) {
        submitCashIn();
      } else {
        // hiển thị báo lỗi không liên kết
        emit(
          state.copyWith(
            isLoading: false,
            titleErrorMessageDialog: S.current.money_notification_cash_in,
            errorMessageDialog:
                S.current.money_account_is_not_linked_to_the_delegation,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          titleErrorMessageDialog: S.current.money_notification_cash_in,
          errorMessageDialog: await getErrorMessage(e),
        ),
      );
    }
  }

  Future<void> getAccountBalance() async {
    try {
      emit(state.copyWith(isLoading: true));
      final result = await _repository.getAccountBalance();
      emit(
        state.copyWith(
          maxAmount: int.parse(result.availableBalance ?? '0'),
          isLoading: false,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: await getErrorMessage(e),
          maxAmount: 0,
        ),
      );
    }
  }

  void onSelectSuggestMoney(num value) {
    onChangeAmount(value.toFormat3());
  }

  void onChangeAmount(String value) {
    num amount = num.tryParse(value.replaceAll(',', '')) ?? 0;
    emit(state.copyWith(money: amount));
    String? message;
    if (amount < 1 && value.isNotEmpty) {
      message = S.current.money_min_value_transfer;
    } else if (amount > (state.maxAmount ?? 0)) {
      message = S.current.money_over_transfer_money;
    }
    emit(state.copyWith(messageValidate: message));
  }

  Future<void> submitCashIn() async {
    if (state.selectBank?.getPartnerCodeStatus != PartnerCodeStatus.VPB) {
      // handleApiCashInMB();
    } else {
      handleApiCashInVPB();
    }
  }

  Future<void> handleApiCashInVPB() async {
    try {
      emit(state.copyWith(isLoading: true));
      final BaseResponse<InitalCashInModel> result = await _repository
          .postInitialCashInInput(
            amount: state.money,
            accountType: state.selectSubAccount?.accountTypeOld ?? "",
          );
      emit(state.copyWith(isLoading: false));
      if (result.isSuccess) {
        emit(
          state.copyWith(
            webViewPageV2Arg: WebViewPageV2Arg(
              location: result.data?.location,
              isOTPFromCashInput: true,
            ),
          ),
        );
      } else {
        emit(state.copyWith(isLoading: false, errorMessage: result.message));
      }
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: await getErrorMessage(e),
        ),
      );
    }
  }

  // todo: Cần test lại
  void setClearInputCash() {
    emit(state.copyWith(selectBank: null, money: 0));
  }

  void resetErrorMessage() {
    emit(
      state.copyWith(
        errorMessage: null,
        errorMessageDialog: null,
        titleErrorMessageDialog: null,
        webViewPageV2Arg: null,
      ),
    );
  }
}
