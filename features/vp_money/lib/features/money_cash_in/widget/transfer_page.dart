import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:screenshot/screenshot.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_money/core/common/utils/copy_utils.dart';
import 'package:vp_money/features/money_cash_in/cubit/account_link_cubit/account_link_cubit.dart';
import 'package:vp_money/generated/assets.gen.dart';
import 'package:vp_money/generated/l10n.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';

import '../cubit/money_cash_in/money_cash_in_cubit.dart';

class TransferPage extends StatefulWidget {
  const TransferPage({super.key});

  @override
  State<TransferPage> createState() => _TransferPageState();
}

class _TransferPageState extends State<TransferPage> {
  ScreenshotController screenshotController = ScreenshotController();
  void _shareQrcode() {
    context.read<AccountLinkCubit>().shareQrcode(screenshotController);
  }

  void _saveImageQr() {
    final moneyCashInState = context.read<MoneyCashInCubit>().state;
    final accountNumber = moneyCashInState.selectVirAccount?.virAccountId;
    context.read<AccountLinkCubit>().saveImageQr(
      screenshotController,
      accountNumber: accountNumber,
    );
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final colorUtils = Theme.of(context);
    return MultiBlocListener(
      listeners: [
        BlocListener<AccountLinkCubit, AccountLinkState>(
          listener: (context, state) {
            if (state.successMessage != null) {
              context.showSuccess(content: state.successMessage!);
              context.read<AccountLinkCubit>().resetSuccessMessage();
            }
            if (state.errorMessage != null) {
              context.showError(content: state.errorMessage!);
              context.read<AccountLinkCubit>().resetErrorMessage();
            }
          },
        ),
      ],
      child: BlocBuilder<MoneyCashInCubit, MoneyCashInState>(
        builder: (context, state) {
          return Padding(
            padding: const EdgeInsets.all(16),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Center(
                    child: Text(
                      S.current.money_information_transfer,
                      style: context.textStyle.subtitle14?.copyWith(
                        color: colorUtils.black,
                      ),
                    ),
                  ),
                  SizedBox(height: 12),
                  Text(
                    S.current.money_vpbank_info,
                    style: context.textStyle.subtitle14?.copyWith(
                      color: colorUtils.gray700,
                    ),
                  ),
                  SizedBox(height: 12),
                  Container(
                    height: 72,
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: colorUtils.gray100,
                    ),
                    child: GestureDetector(
                      onTap: () {
                        copyToClipboard(
                          context,
                          state.selectVirAccount?.virAccountId,
                        );
                      },
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Assets.images.icLogoBank.image(height: 40, width: 40),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'VPBANKS - ${state.selectVirAccount?.virAccountName}',
                                  overflow: TextOverflow.ellipsis,
                                  style: context.textStyle.body14?.copyWith(
                                    color: colorUtils.gray700,
                                  ),
                                ),
                                Text(
                                  state.selectVirAccount?.virAccountId ?? "-",
                                  overflow: TextOverflow.ellipsis,
                                  style: context.textStyle.subtitle14?.copyWith(
                                    color: colorUtils.gray700,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 10),
                          CircleAvatar(
                            radius: 16,
                            backgroundColor: colorUtils.bgMain,
                            child: Assets.icons.icCopy.svg(),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 12),
                  Center(
                    child: Text(
                      S.current.money_scan_qr,
                      style: context.textStyle.body14?.copyWith(
                        color: colorUtils.gray700,
                      ),
                    ),
                  ),
                  SizedBox(height: 12),

                  Screenshot(
                    controller: screenshotController,
                    child: Center(
                      child: BlocBuilder<MoneyCashInCubit, MoneyCashInState>(
                        builder: (context, state) {
                          var addInfo =
                              "Nap tien TK ${state.selectVirAccount?.virAccountId} tai VPBANKS";
                          return CachedNetworkImage(
                            width: 200,
                            height: 200,
                            imageUrl:
                                'https://img.vietqr.io/image/VPB-${state.selectVirAccount?.virAccountId}-qr_only.png?addInfo=$addInfo',
                            errorWidget:
                                (_, __, ___) => GestureDetector(
                                  onTap: () {},
                                  child: Assets.icons.icRetryQrCashin.svg(
                                    width: 200,
                                    height: 200,
                                  ),
                                ),
                          );
                        },
                      ),
                    ),
                  ),

                  SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      TextButton.icon(
                        onPressed: () => _saveImageQr(),
                        icon: Assets.icons.icDownload.svg(),
                        label: Text(
                          S.current.money_download,
                          style: context.textStyle.body14?.copyWith(
                            color: colorUtils.gray700,
                          ),
                        ),
                      ),
                      const SizedBox(width: 20),
                      TextButton.icon(
                        onPressed: () => _shareQrcode(),
                        icon: Assets.icons.icShare.svg(),
                        label: Text(
                          S.current.money_share,
                          style: context.textStyle.body14?.copyWith(
                            color: colorUtils.gray700,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
