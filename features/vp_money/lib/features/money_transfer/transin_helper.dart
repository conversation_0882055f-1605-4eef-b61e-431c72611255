import 'package:collection/collection.dart';
import 'package:tiengviet/tiengviet.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_money/generated/l10n.dart';
import 'package:vp_money/model/request/money_transfer/deposit_cash_on_hand_params.dart';
import 'package:vp_money/model/response/money_transfer/money_account_recived_obj.dart';

class TransinHelper {
  /*
    Hàm này lấy danh sách các tài khoản phụ không phải là trái phiếu hoặc phái sinh, chuyển đổi chúng thành các đối tượng `MoneyAccountRecivedObj`, sau đó lọc ra bất kỳ tài khoản nào là tài khoản hoa hồng hoặc tài khoản bên ngoài. Sau đó, danh sách kết quả được trả về.
    */
  static List<MoneyAccountRecivedObj> getReceivedAccounts() {
    final data =
        GetIt.instance<SubAccountCubit>().state.subAccountsAll
            .where((e) => !e.isBond && !e.isDerivative)
            .toList();

    final value = data.map((e) => convertToAccountReceived(e)).toList();

    value.removeWhere(
      (e) =>
          e.benefitAccount ==
              GetIt.instance<SubAccountCubit>().commissionAccount?.id ||
          e.isOuternal(),
    );
    return value;
  }

  static String? getTitleSubAccount(SubAccountModel? subAccountModel) {
    if (subAccountModel == null) return null;
    return subAccountModel.subAccountProductTypeCdEnum.displayName;
  }

  static String getTextSelectReceiveAccountBank(TransferAccountModel? account) {
    if (account != null) {
      return '${account.bankCode ?? ''} - ${account.benefitName ?? ''}';
    } else {
      return S.current.money_select_bank;
    }
  }

  static String? getTextSelectReceiveAccount(TransferAccountModel? account) {
    if (account != null) {
      return account.typeAccount?.displayName;
    } else {
      return S.current.money_select_sub_account;
    }
  }

  static MoneyAccountRecivedObj convertToAccountReceived(
    SubAccountModel model,
  ) {
    return MoneyAccountRecivedObj(
      benefitAccount: model.id,
      transfertype: MoneyTransferType.internal.name,
      benefitName: model.accountType.toAccountConstants(),
    );
  }

  static SubAccountModel? getSubAccountModelFromMoneyAccount(
    MoneyAccountRecivedObj? moneyAccount,
  ) {
    try {
      final receivedAccount = GetIt.instance<SubAccountCubit>()
          .subAccountsAllNoCommissionHaveDerivativeAccount
          .firstWhere(
            (element) => element.id == moneyAccount?.benefitAccount,
            orElse: () => SubAccountModel(),
          );
      return receivedAccount;
    } catch (e) {
      return null;
    }
  }

  static bool isNormalOrMarginAccount(SubAccountModel? receivedAccount) {
    return (receivedAccount?.isNormal ?? false) ||
        (receivedAccount?.isMargin ?? false);
  }

  static String getHintTextContentTransferDefault() {
    final username =
        GetIt.instance<AuthCubit>().userInfo?.userinfo?.fullname ?? '';
    return TiengViet.parse('$username ${S.current.money_transfer_money}');
  }

  static String getTransferMessage({
    SubAccountModel? transferAccount,
    TransferAccountModel? received,
  }) {
    final username =
        GetIt.instance<AuthCubit>().userInfo?.userinfo?.fullname ?? '';
    final nameTransferAccount =
        transferAccount?.subAccountProductTypeCdEnum.shortName;
    final nameReceived = received?.typeAccount?.shortName;
    if ((transferAccount != null) && (received != null)) {
      return TiengViet.parse(
        "$username Chuyển tiền từ $nameTransferAccount đến $nameReceived",
      );
    }

    return getHintTextContentTransferDefault();
  }

  static List<MoneyAccountRecivedObj>
  getReceivedSubAccountsAddDerivativeSubAccount(
    List<MoneyAccountRecivedObj> data,
    SubAccountModel? subAccountModel,
  ) {
    if (GetIt.instance<SubAccountCubit>()
            .state
            .derivativeSubAccount
            .isNotEmpty &&
        !(subAccountModel?.isBond ?? false)) {
      final derivativeSubAccount =
          GetIt.instance<SubAccountCubit>().state.derivativeSubAccount.first;
      data.add(convertToAccountReceived(derivativeSubAccount));
      return data;
    }
    return data;
  }

  static DepositCashOnHandParams getWithDrawAndDepositOnHandDerivativeParams({
    SubAccountModel? transfer,
    MoneyAccountRecivedObj? receive,
    required num amountTransfer,
    required String transDescrtiption,
    bool fromDerivative = false,
  }) {
    String accountFlexSrc = '';
    String source = '';
    if (fromDerivative) {
      final receivedAccount = GetIt.instance<SubAccountCubit>()
          .subAccountsAllNoCommissionHaveDerivativeAccount
          .firstWhereOrNull((element) => element.id == receive?.benefitAccount);
      accountFlexSrc = receivedAccount?.id ?? '';
      source = receivedAccount!.accountType.toAccountConstants() ?? '';
    } else {
      source = transfer?.accountType.toAccountConstants() ?? '';
      accountFlexSrc = transfer?.id ?? '';
    }
    final amount = amountTransfer;
    final desc = transDescrtiption;
    source = source.replaceAll('.', '0');
    final params = DepositCashOnHandParams(
      accountFlexSrc: accountFlexSrc,
      amount: amount,
      desc: desc,
      source: source,
    );
    return params;
  }

  static String getHintTextContentTransferBank(TransferAccountModel? received) {
    final username =
        GetIt.instance<AuthCubit>().userInfo?.userinfo?.fullname ?? '';
    final nameReceived = received?.typeAccount?.shortName;
    if ((received != null)) {
      return TiengViet.parse(
        '$username Chuyển tiền từ $nameReceived ra ngân hàng',
      );
    }
    return TiengViet.parse('$username Chuyển tiền  ra ngân hàng');
  }
}
