import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_money/core/common/widget/money_button_old.dart';
import 'package:vp_money/features/money_transfer/cubit/transin/transin_cubit.dart';
import 'package:vp_money/features/money_transfer/transin_helper.dart';
import 'package:vp_money/features/money_transfer/widget/select_bank_account.dart';
import 'package:vp_money/generated/l10n.dart';
import 'package:vp_money/model/response/money_transfer/money_account_recived_obj.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_stock_common/screens/accoun_list/bottom_sheet_account_list/money_sub_account_bottom_sheet.dart';

class TransinScreen extends StatefulWidget {
  const TransinScreen({super.key});

  @override
  State<TransinScreen> createState() => _TransinScreenState();
}

class _TransinScreenState extends State<TransinScreen> {
  final FocusNode _focus = FocusNode();
  final FocusNode _focusContent = FocusNode();
  bool showSuggest = false;
  final TextEditingController _moneyController = TextEditingController();
  final TextEditingController _contentController = TextEditingController();
  @override
  void initState() {
    super.initState();

    _focus.addListener(() {
      setState(() {
        showSuggest = _focus.hasFocus;
      });
    });
    _focusContent.addListener(() {
      if (!_focusContent.hasFocus) {
        //  _bloc.convertContent();
      }
    });
  }

  @override
  void dispose() {
    _moneyController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorUtils = Theme.of(context);
    return BlocConsumer<TransinCubit, TransInState>(
      listener: (context, state) {
        if (state.errorMessage != null) {
          context.showError(content: state.errorMessage ?? "-");
          context.read<TransinCubit>().resetErrorMessage();
        }
      },
      builder: (context, state) {
        return VPLoadingBuilder(
          showLoading: state.isLoading,
          builder: (_, child) => child!,
          child: GestureDetector(
            onTap: () {
              FocusScope.of(context).requestFocus(FocusNode());
            },
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            S.current.money_filter_with_sub_account,
                            style: context.textStyle.subtitle14?.copyWith(
                              color: context.colors.textPrimary,
                            ),
                          ),
                          SizedBox(height: 8),
                          VPDropdownView.large(
                            hint: 'Tiểu khoản ký quỹ',
                            width: double.infinity,
                            value: TransinHelper.getTitleSubAccount(
                              state.transfer,
                            ),
                            onTap:
                                () async =>
                                    _showDerivativeSubAccountBottomSheet(
                                      context,
                                    ),
                          ),
                          SizedBox(height: 16),

                          Text(
                            "Loại tiểu khoản thụ hưởng (nội bộ)",
                            style: context.textStyle.subtitle14?.copyWith(
                              color: context.colors.textPrimary,
                            ),
                          ),
                          SizedBox(height: 8),
                          VPDropdownView.large(
                            hint: S.current.money_select_sub_account,
                            width: double.infinity,
                            value: TransinHelper.getTextSelectReceiveAccount(
                              state.receive,
                            ),
                            onTap:
                                () async => _showSelectBankAccountBottomSheet(
                                  context,
                                  state.listAccountReceive,
                                ),
                          ),

                          SizedBox(height: 16),
                          VPTextField.medium(
                            labelText: 'Số tiền chuyển',
                            hintText: '0',
                            keyboardType: TextInputType.number,
                            controller: _moneyController,
                            inputType:
                                state.checkEnableInputAmount
                                    ? ((state.message?.isEmpty ?? true)
                                        ? InputType.rest
                                        : InputType.error)
                                    : InputType.disabled,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                              CurrencyInputFormatter(),
                            ],
                            focusNode: _focus,
                            onChanged: (value) {
                              if (value.isEmpty) {
                                context.read<TransinCubit>().onChangeAmount(
                                  '0',
                                );
                                return;
                              }
                              context.read<TransinCubit>().onChangeAmount(
                                value,
                              );
                            },
                            suffixIcon:
                                (a) => Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Text(
                                      'đ',
                                      textAlign: TextAlign.center,
                                      style: vpTextStyle.body14?.copyWith(
                                        color: colorUtils.gray500,
                                      ),
                                    ),
                                  ],
                                ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 16, top: 8),
                            child: Text(
                              (state.message?.isEmpty ?? true)
                                  ? '${S.current.money_max_cash_input}: ${(state.maxAmount ?? 0).toMoney()}'
                                  : state.message ?? "",
                              style: context.textStyle.captionMedium?.copyWith(
                                color:
                                    (state.message?.isEmpty ?? true)
                                        ? colorUtils.gray900
                                        : colorUtils.red,
                              ),
                            ),
                          ),
                          SizedBox(height: 16),
                          VPTextField.medium(
                            controller: _contentController,
                            keyboardType: TextInputType.text,
                            labelText: S.current.money_transfer_content,
                            hintText: TransinHelper.getTransferMessage(
                              received: state.receive,
                              transferAccount: state.transfer,
                            ),
                            inputType:
                                state.receive != null
                                    ? InputType.rest
                                    : InputType.disabled,
                            focusNode: _focusContent,
                            maxLength: 200,
                            onChanged: (value) {
                              if (value.trim().isEmpty) {
                                context
                                    .read<TransinCubit>()
                                    .updateContentTransferRequest(
                                      TransinHelper.getTransferMessage(
                                        received: state.receive,
                                        transferAccount: state.transfer,
                                      ),
                                    );
                              } else {
                                context
                                    .read<TransinCubit>()
                                    .updateContentTransferRequest(value);
                              }
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const DividerWidget(),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 16,
                    horizontal: 8,
                  ),
                  child: MoneyButtonOldWidget(
                    action: S.current.money_transfers,
                    enable: state.checkEnableTransfer,
                    onPressed: () {
                      if (_contentController.text.trim().isEmpty) {
                        context
                            .read<TransinCubit>()
                            .updateContentTransferRequest(
                              TransinHelper.getTransferMessage(
                                received: state.receive,
                                transferAccount: state.transfer,
                              ),
                            );
                      }
                      context.read<TransinCubit>().handleOnPressTransferMoney();
                    },
                  ),
                ),
                _showSugesstion(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _showSugesstion() {
    final colorUtils = Theme.of(context);
    return BlocBuilder<TransinCubit, TransInState>(
      builder: (context, state) {
        return Visibility(
          visible: showSuggest && (state.maxAmount ?? 0) > 0,
          child: Container(
            width: double.infinity,
            color: colorUtils.highlightBg,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            alignment: Alignment.center,
            child:
                (state.amount == 0 || state.amount > 10000)
                    ? InkWell(
                      onTap: () {
                        _moneyController.text =
                            (state.maxAmount ?? 0).toFormat3();
                        _moneyController.selection = TextSelection.fromPosition(
                          TextPosition(offset: _moneyController.text.length),
                        );
                        context.read<TransinCubit>().onSelectSuggestMoney(
                          state.maxAmount ?? 0,
                        );
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Text(
                          '${S.current.money_all}: ${(state.maxAmount ?? 0).toFormat3()}',
                          style: vpTextStyle.body14?.copyWith(
                            color: colorUtils.gray900,
                          ),
                        ),
                      ),
                    )
                    : Row(
                      children: List.generate(3, (index) {
                        final multipliers = [100000, 1000000, 10000000];
                        num value = state.amount * multipliers[index];
                        return Expanded(
                          child: InkWell(
                            onTap: () {
                              _moneyController.text = (value).toFormat3();
                              _moneyController
                                  .selection = TextSelection.fromPosition(
                                TextPosition(
                                  offset: _moneyController.text.length,
                                ),
                              );
                              context.read<TransinCubit>().onSelectSuggestMoney(
                                value,
                              );
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                border:
                                    index > 0
                                        ? Border(
                                          left: BorderSide(
                                            color: colorUtils.gray500,
                                          ),
                                        )
                                        : const Border(),
                              ),
                              margin: const EdgeInsets.symmetric(vertical: 8),
                              alignment: Alignment.center,
                              child: Text(
                                value.toFormat3(),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: vpTextStyle.body14?.copyWith(
                                  color: colorUtils.gray900,
                                ),
                              ),
                            ),
                          ),
                        );
                      }),
                    ),
          ),
        );
      },
    );
  }

  void _showSelectBankAccountBottomSheet(
    BuildContext context,
    List<TransferAccountModel>? bankAccounts,
  ) async {
    final result = await showSelectBankAccountBottomSheet(
      context: context,
      bankAccounts: bankAccounts ?? [],
    );
    if (result is! TransferAccountModel || !context.mounted) return;
    context.read<TransinCubit>().selectReceiveAccount(result);
    _moneyController.text = '';
    context.read<TransinCubit>().onChangeAmount('');
  }

  void _showDerivativeSubAccountBottomSheet(BuildContext context) async {
    final listSubAccounts =
        GetIt.instance<SubAccountCubit>().getSubAccountsAllTransfer;
    final result = await showDerivativeSubAccountBottomSheet(
      context: context,
      listSubAccounts: listSubAccounts,
    );
    if (result is! SubAccountModel || !context.mounted) return;
    context.read<TransinCubit>().handleSelectTransferAccount(result);
    _moneyController.text = '';
    context.read<TransinCubit>().onChangeAmount('');
  }
}
