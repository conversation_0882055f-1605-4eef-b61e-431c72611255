import 'package:flutter/material.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_money/core/common/widget/money_button_default.dart';
import 'package:vp_money/features/money_transfer/transin_helper.dart';
import 'package:vp_money/generated/l10n.dart';
import 'package:vp_money/model/response/money_transfer/money_account_recived_obj.dart';

class MoneyTransferAccountModal extends StatelessWidget {
  const MoneyTransferAccountModal({super.key, required this.listObj});

  final List<TransferAccountModel> listObj;

  @override
  Widget build(BuildContext context) {
    final colorUtils = Theme.of(context);
    return SafeArea(
      bottom: true,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 24),
        child: IntrinsicHeight(
          child: Column(
            children: [
              Container(
                width: double.maxFinite,
                decoration: BoxDecoration(
                  color: colorUtils.bgPopup,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.7,
                  ),
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Column(
                          children: List.generate(listObj.length, (index) {
                            final item = listObj[index];
                            return MoneyTransferItemAccount(
                              isLastIndex: index == listObj.length - 1,
                              obj: item,
                              callBack: (obj) {
                                Navigator.pop(context, obj);
                              },
                            );
                          }),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              SizedBox(
                height: 50,
                child: MoneyButtonDefault(
                  textStyle: vpTextStyle.body16?.copyWith(
                    color: colorUtils.red,
                  ),
                  color: colorUtils.bgPopup,
                  text: S.current.money_cancel,
                  press: () {
                    Navigator.pop(context);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class MoneyTransferItemAccount extends StatelessWidget {
  const MoneyTransferItemAccount({
    super.key,
    required this.obj,
    required this.callBack,
    required this.isLastIndex,
  });
  final TransferAccountModel obj;
  final Function(TransferAccountModel) callBack;
  final bool isLastIndex;

  @override
  Widget build(BuildContext context) {
    final colorUtils = Theme.of(context);
    return InkWell(
      onTap: () {
        callBack(obj);
      },
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(14),
            child: Text(
              TransinHelper.getTextSelectReceiveAccountBank(obj),
              // obj.typeAccount?.displayName ?? "-",
              style: vpTextStyle.body16?.copyWith(color: colorUtils.gray700),
              textAlign: TextAlign.center,
            ),
          ),
          Visibility(visible: !isLastIndex, child: DividerWidget()),
        ],
      ),
    );
  }
}
