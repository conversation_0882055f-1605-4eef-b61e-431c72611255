import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:tiengviet/tiengviet.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/cubit/sub_account_cubit.dart';
import 'package:vp_core/model/sign_in_model/sub_account_model.dart';
import 'package:vp_core/model/sign_in_model/sub_account_type.dart';
import 'package:vp_money/core/repository/money_repository.dart';
import 'package:vp_money/generated/l10n.dart';
import 'package:vp_money/model/request/money_transfer/money_transfer_in_request.dart';
import 'package:vp_money/model/response/money_transfer/money_account_recived_obj.dart';
import 'package:vp_money/model/response/money_transfer/money_fee_atm_obj.dart';

part 'transfer_bank_cubit.freezed.dart';
part 'transfer_bank_state.dart';

class TransferBankCubit extends Cubit<TransferBankState> {
  TransferBankCubit() : super(TransferBankState());
  final MoneyRepository _repository = GetIt.instance<MoneyRepository>();

  void init() {
    if (state.transfer == null) {
      final subAccounts = GetIt.instance<SubAccountCubit>().defaultSubAccount;
      selectTransferAccount(subAccounts);
    }
  }

  void selectTransferAccount(SubAccountModel? transfer) {
    if (transfer != null && transfer.id != state.transfer?.id) {
      emit(
        state.copyWith(
          transfer: transfer,
          receive: null,
          maxAmount: null,
          amount: 0,
          fee: 0,
          listAccountReceive: [],
          message: '',
        ),
      );
      getInfo();
    }
  }

  void selectReceiveAccount(TransferAccountModel? receive) {
    if (receive != null) {
      emit(state.copyWith(receive: receive));
    }
  }

  void getInfo() async {
    emit(state.copyWith(isLoading: true));
    try {
      await Future.wait([getTransferAccountList(), getCiInfo()]);
      emit(state.copyWith(isLoading: false));
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: await getErrorMessage(e),
        ),
      );
    }
  }

  //lấy danh sách ngân hàng
  Future getTransferAccountList() async {
    emit(state.copyWith(listAccountReceive: []));
    final value = await _repository.transferAccountList(
      state.transfer?.id ?? '',
      MoneyTransferType.outernal.name,
    );
    emit(state.copyWith(listAccountReceive: value));
  }

  //lấy số dư tối đa
  Future getCiInfo() async {
    num? maxMoney;
    maxMoney = await _repository
        .availableCashTransferOnline(state.transfer?.id ?? "0")
        .then((e) {
          return e.availableTransferOnline;
        });
    emit(state.copyWith(maxAmount: maxMoney));
  }

  void onChangeAmount(String value) {
    num amount = num.tryParse(value.replaceAll(',', '')) ?? 0;
    emit(state.copyWith(amount: amount));
    String message = '';
    if (amount >= 1 && amount <= (state.maxAmount ?? 0)) {
      callApiFeeAmt(amount);
    } else {
      if (amount < 1 && value.isNotEmpty) {
        message = S.current.money_min_value_transfer2;
      } else if (amount > (state.maxAmount ?? 0)) {
        // message = getMoneyLang(MoneyKeyLang.overTransferMoney);
        message =
            '${S.current.money_max_cash_input}: ${(state.maxAmount ?? 0).toMoney()}';
      }
    }
    emit(state.copyWith(message: message, amount: amount));
  }

  //Lấy phí chuyển tiền
  void callApiFeeAmt(num moneyText) async {
    if (state.transfer?.toSubAccountType == SubAccountType.derivative) return;
    try {
      MoneyFeeAmtObj? freeAtmObj = await _repository.feeAmt(
        state.transfer?.id ?? '',
        moneyText,
      );
      num feeTransfer = freeAtmObj.feeamt ?? 0;
      emit(state.copyWith(fee: feeTransfer));
    } catch (e) {
      emit(state.copyWith(errorMessage: await getErrorMessage(e)));
    }
  }

  void onSelectSuggestMoney(num value) {
    onChangeAmount(value.toFormat3());
  }

  //*Cập nhật lại request Nội dung chuyển tiền
  void updateContentTransferRequest(String description) {
    var transferRequestObj = state.transferRequestObj.copyWith(
      description: convertText(description),
    );
    emit(state.copyWith(transferRequestObj: transferRequestObj));
  }

  String convertText(String text) {
    String regex =
        r'[^\p{Alphabetic}\p{Mark}\p{Decimal_Number}\p{Connector_Punctuation}\p{Join_Control}\s\-]+';
    return TiengViet.parse(text.replaceAll(RegExp(regex, unicode: true), ''));
  }

  void resetErrorMessage() {
    emit(state.copyWith(errorMessage: null));
  }

  void resetHandleOnPressTransferMoney() {
    emit(state.copyWith(handleOnPressTransferMoney: null));
  }

  void resetCheckValidateAmount() {
    emit(state.copyWith(checkValidateAmount: null));
  }

  Future<void> handleOnPressTransferMoney() async {
    var obj = state.receive;
    var transferRequestObj = state.transferRequestObj.copyWith(
      accountId: state.transfer?.id ?? '',
      benefitAccountId: obj?.benefitAccountId ?? '',
      amount: state.amount,
      requestId: AppHelper().genXRequestID(),
    );
    emit(state.copyWith(transferRequestObj: transferRequestObj));
    final transferContinue = checkAmount();
    if (transferContinue == false) {
      emit(state.copyWith(checkValidateAmount: false));
      return;
    }
    // xử lý gọi api chuyển tiền ngân hàng
    emit(state.copyWith(handleOnPressTransferMoney: true));
  }

  bool checkAmount() {
    if (!state.transferRequestObj.invalidMin &&
        !state.transferRequestObj.invalidMax) {
      return true;
    }
    return false;
  }
}
