import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tuple/tuple.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_money/core/common/widget/money_context_extensions.dart';
import 'package:vp_money/features/money_history/cubit/money_history_cubit.dart';
import 'package:vp_money/features/money_history/enum/money_account_type_enum.dart';
import 'package:vp_money/features/money_history/enum/money_time_filter_enum.dart';
import 'package:vp_money/features/money_history/widget/money_history_list.dart';
import 'package:vp_money/features/money_history/widget/money_history_modal_filter.dart';
import 'package:vp_money/features/money_history/widget/money_history_type_transfer.dart';
import 'package:vp_money/generated/assets.gen.dart';
import 'package:vp_money/generated/l10n.dart';

class MoneyHistoryScreen extends StatefulWidget {
  const MoneyHistoryScreen({super.key});

  @override
  State<MoneyHistoryScreen> createState() => _MoneyHistoryScreenState();
}

class _MoneyHistoryScreenState extends State<MoneyHistoryScreen> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => MoneyHistoryCubit()..initData(),
      child: BlocConsumer<MoneyHistoryCubit, MoneyHistoryState>(
        listener: (context, state) {
          if (state.errorMessage != null) {
            context.moneyShowSnackBar(
              content: state.errorMessage ?? "-",
              snackBarType: VPSnackBarType.error,
            );
            context.read<MoneyHistoryCubit>().resetErrorMessage();
          }
        },
        builder: (context, state) {
          return VPScaffold(
            backgroundColor: vpColor.backgroundElevationMinus1,
            body: SafeArea(
              child: Column(
                children: [
                  HeaderWidget(
                    subTitle: S.current.money_assets,
                    title: S.current.money_history_money,
                    actionBack: () {
                      Navigator.pop(context);
                    },
                    actionRight: () {
                      showModalFilter(context);
                    },

                    icon:
                        state.checkEnableFilter
                            ? Assets.icons.icHaveFilter.svg()
                            : Assets.icons.icFilter.svg(),
                  ),
                  MoneyTypeChoiceChips(),
                  _subAccount(),
                  Expanded(child: MoneyHistoryList()),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _subAccount() {
    final colorUtils = Theme.of(context);
    return BlocBuilder<MoneyHistoryCubit, MoneyHistoryState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Text(
                "Tiểu khoản",
                style: context.textStyle.subtitle16?.copyWith(
                  color: colorUtils.black,
                ),
              ),
              Spacer(),
              Text(
                state.selectedAccount.displayName,
                style: context.textStyle.subtitle16?.copyWith(
                  color: colorUtils.black,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /* -------- Show modal filter --------*/
  Future<void> showModalFilter(BuildContext context) async {
    final bloc = context.read<MoneyHistoryCubit>();

    final colorUtils = Theme.of(context);
    var filter = await showModalBottomSheet<dynamic>(
      barrierColor: colorUtils.overlayBottomSheet,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Padding(
          padding: MediaQuery.of(context).viewInsets,
          child: MoneyHistoryModalFilter(
            accountTypeInit: bloc.state.selectedAccount,
            moneyTimeFilterInit: bloc.state.selectedTime,
            dateTimeRangeCustom: bloc.state.customDateRange,
          ),
        );
      },
    );
    if (filter
        is Tuple3<AccountTypeEnum, MoneyTimeFilterEnum, DateTimeRange?>) {
      final accountType = filter.item1;
      final timeFilter = filter.item2;
      final thirdValue = filter.item3;
      context.read<MoneyHistoryCubit>().changeFilter(
        accountType,
        timeFilter,
        thirdValue,
      );
    }

    // _handleAfterClosePopup(objFilter, context);
  }
}
