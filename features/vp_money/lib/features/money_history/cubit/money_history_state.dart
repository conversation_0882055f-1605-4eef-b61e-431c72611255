part of 'money_history_cubit.dart';

@freezed
abstract class MoneyHistoryState with _$MoneyHistoryState {
  const factory MoneyHistoryState({
    @Default([]) List<MoneyCashTransferHistResponseObj> listTransactions,
    @Default(AccountTypeEnum.normal) AccountTypeEnum selectedAccount,
    @Default(MoneyTimeFilterEnum.oneMonth) MoneyTimeFilterEnum selectedTime,
    @Default(MoneyTransferTypeEnum.all)
    MoneyTransferTypeEnum selectedTransferType,
    DateTimeRange? customDateRange,
    @Default(false) bool isLoading,
    String? errorMessage,
  }) = _MoneyHistoryState;

  factory MoneyHistoryState.initial() => MoneyHistoryState();

  const MoneyHistoryState._();

  bool get checkEnableFilter =>
      selectedTime != MoneyTimeFilterEnum.oneMonth ||
      selectedAccount !=
          AccountTypeEnumExt.checkSelectedAccount(
            GetIt.instance<SubAccountCubit>().defaultSubAccount,
          );
}
