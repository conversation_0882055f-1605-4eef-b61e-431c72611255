import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_money/features/money_history/enum/money_transfer_status_enum.dart';
import 'package:vp_money/generated/assets.gen.dart';
import 'package:vp_money/model/response/money_history/money_cash_transfer_hist_obj.dart';

class MoneyHistoryItemWidget extends StatelessWidget {
  const MoneyHistoryItemWidget({super.key, required this.obj});

  final MoneyCashTransferHistResponseObj obj;

  @override
  Widget build(BuildContext context) {
    final colorUtils = Theme.of(context);
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      obj.getTimeTransfer(),
                      style: context.textStyle.captionRegular?.copyWith(
                        color: colorUtils.gray700,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: obj.getStatusFromText?.backgroundColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      obj.getStatusFromText?.displayName ?? "",
                      style: context.textStyle.captionMedium?.copyWith(
                        color: colorUtils.black,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Text(
                    obj.getTypeTransfer(context),
                    style: context.textStyle.subtitle14?.copyWith(
                      color: colorUtils.gray700,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      obj.getMoneyTransfer(),
                      style: context.textStyle.subtitle14?.copyWith(
                        color: colorUtils.black,
                      ),
                      textAlign: TextAlign.end,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      obj.getFromAcc(),
                      style: context.textStyle.body14?.copyWith(
                        color: colorUtils.black,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(width: 8),
                  AppIconBg(
                    size: 35,
                    padding: 12,
                    icon: Assets.icons.icArrowRight2.svg(),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      obj.getToAcc(),
                      style: context.textStyle.body14?.copyWith(
                        color: colorUtils.black,
                      ),

                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        DividerWidget(),
      ],
    );
  }
}
