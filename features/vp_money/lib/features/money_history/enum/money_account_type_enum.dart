import 'package:vp_core/model/sign_in_model/sub_account_model.dart';
import 'package:vp_core/model/sign_in_model/sub_account_type.dart';

enum AccountTypeEnum { normal, margin, derivative }

extension AccountTypeEnumExt on AccountTypeEnum {
  String get displayName {
    switch (this) {
      case AccountTypeEnum.normal:
        return 'Thường';
      case AccountTypeEnum.margin:
        return 'Ký quỹ';
      case AccountTypeEnum.derivative:
        return '<PERSON><PERSON>i sinh';
    }
  }

  SubAccountType get subAccountType {
    switch (this) {
      case AccountTypeEnum.normal:
        return SubAccountType.normal;
      case AccountTypeEnum.margin:
        return SubAccountType.margin;
      case AccountTypeEnum.derivative:
        return SubAccountType.derivative;
    }
  }

  static AccountTypeEnum checkSelectedAccount(
    SubAccountModel defaultSubAccount,
  ) {
    switch (defaultSubAccount.toSubAccountType) {
      case SubAccountType.normal:
        return AccountTypeEnum.normal;
      case SubAccountType.margin:
        return AccountTypeEnum.margin;
      case SubAccountType.derivative:
        return AccountTypeEnum.derivative;
      default:
        return AccountTypeEnum.normal;
    }
  }
}
