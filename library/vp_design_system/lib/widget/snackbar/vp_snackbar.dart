import 'package:flutter/material.dart';
import 'package:vp_design_system/gen/assets.gen.dart';
import 'package:vp_design_system/themes/utils.dart';
import 'package:vp_design_system/themes/vp_shadow.dart';

enum VPSnackBarType {
  success,
  error,
  alert,
  information;

  Widget get icon {
    return switch (this) {
      success =>
        DesignAssets.icons.snackBar.icSuccess.svg(width: 24, height: 24),
      error => DesignAssets.icons.snackBar.icError.svg(width: 24, height: 24),
      alert => DesignAssets.icons.snackBar.icAlert.svg(width: 24, height: 24),
      information =>
        DesignAssets.icons.snackBar.icInformation.svg(width: 24, height: 24),
    };
  }
}

class VpSnackBarView extends StatelessWidget {
  const VpSnackBarView({
    required this.snackBarType,
    required this.content,
    super.key,
  });

  final VPSnackBarType snackBarType;

  final String content;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: context.colors.backgroundElevation2,
        borderRadius: BorderRadius.circular(8),
        boxShadow: VPBoxShadow.shadowElevation2,
      ),
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
      child: Row(
        children: [
          snackBarType.icon,
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              content,
              style: context.textStyle.body14
                  ?.copyWith(color: context.colors.textPrimary),
            ),
          ),
        ],
      ),
    );
  }
}
