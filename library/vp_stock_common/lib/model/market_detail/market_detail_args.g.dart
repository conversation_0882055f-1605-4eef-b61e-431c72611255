// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'market_detail_args.dart';

// **************************************************************************
// FromQueryParamsGenerator
// **************************************************************************

extension MarketDetailArgsExtensions on MarketDetailArgs {
  static MarketDetailArgs fromQueryParams(Map<String, String> params) {
    return MarketDetailArgs(
      initialPage: int.tryParse(params['initialPage'] ?? ''),
      indexCode:
          _tryParseEnumIndexCode(params['indexCode']) ?? IndexCode.VNINDEX,
    );
  }

  Map<String, String> toQueryParams() {
    return {
      if (initialPage != null) 'initialPage': initialPage!.toString(),
      'indexCode': indexCode.name,
    };
  }

  static IndexCode? _tryParseEnumIndexCode(String? name) {
    if (name == null) return null;
    return IndexCode.values.cast<IndexCode?>().firstWhere(
      (e) => e!.name == name,
      orElse: () => null,
    );
  }
}
