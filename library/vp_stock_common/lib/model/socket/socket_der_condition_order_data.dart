import 'package:json_annotation/json_annotation.dart';

part 'socket_der_condition_order_data.g.dart';

@JsonSerializable()
class VPDerConditionOrderData {
  final String? msgCode;
  final String? eventType;
  final String? seq;
  final String? custodyCode;
  final String? accountNo;
  final String? activeTime;
  final String? quoteId;
  final String? execType;
  final String? symbol;
  final String? quoteQtty;
  final String? quotePrice;
  final String? priceType;
  final String? limitPrice;
  final String? activePrice;
  final String? timeType;
  final String? fromDate;
  final String? toDate;
  final String? statusCode;
  final String? deltaValue;
  final String? deltaType;
  final String? priceStep;
  final String? price;
  final String? activePriceSL;
  final String? priceSL;
  final String? priceTP;
  final String? split;
  final String? orderId;
  final String? subQuoteId;
  final String? subOrdertype;
  final String? cancelQtty;
  final String? errorMessage;
  final String? orderType;
  final String? activeType;

  VPDerConditionOrderData({
    this.msgCode,
    this.eventType,
    this.seq,
    this.custodyCode,
    this.accountNo,
    this.activeTime,
    this.quoteId,
    this.execType,
    this.symbol,
    this.quoteQtty,
    this.quotePrice,
    this.priceType,
    this.limitPrice,
    this.activePrice,
    this.timeType,
    this.fromDate,
    this.toDate,
    this.statusCode,
    this.deltaValue,
    this.deltaType,
    this.priceStep,
    this.price,
    this.activePriceSL,
    this.priceSL,
    this.priceTP,
    this.split,
    this.orderId,
    this.subQuoteId,
    this.subOrdertype,
    this.cancelQtty,
    this.errorMessage,
    this.orderType,
    this.activeType,
  });

  factory VPDerConditionOrderData.fromJson(Map<String, dynamic> json) =>
      _$VPDerConditionOrderDataFromJson(json);

  Map<String, dynamic> toJson() => _$VPDerConditionOrderDataToJson(this);
}
