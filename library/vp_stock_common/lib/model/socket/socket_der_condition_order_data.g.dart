// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'socket_der_condition_order_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VPDerConditionOrderData _$VPDerConditionOrderDataFromJson(
  Map<String, dynamic> json,
) => VPDerConditionOrderData(
  msgCode: json['msgCode'] as String?,
  eventType: json['eventType'] as String?,
  seq: json['seq'] as String?,
  custodyCode: json['custodyCode'] as String?,
  accountNo: json['accountNo'] as String?,
  activeTime: json['activeTime'] as String?,
  quoteId: json['quoteId'] as String?,
  execType: json['execType'] as String?,
  symbol: json['symbol'] as String?,
  quoteQtty: json['quoteQtty'] as String?,
  quotePrice: json['quotePrice'] as String?,
  priceType: json['priceType'] as String?,
  limitPrice: json['limitPrice'] as String?,
  activePrice: json['activePrice'] as String?,
  timeType: json['timeType'] as String?,
  fromDate: json['fromDate'] as String?,
  toDate: json['toDate'] as String?,
  statusCode: json['statusCode'] as String?,
  deltaValue: json['deltaValue'] as String?,
  deltaType: json['deltaType'] as String?,
  priceStep: json['priceStep'] as String?,
  price: json['price'] as String?,
  activePriceSL: json['activePriceSL'] as String?,
  priceSL: json['priceSL'] as String?,
  priceTP: json['priceTP'] as String?,
  split: json['split'] as String?,
  orderId: json['orderId'] as String?,
  subQuoteId: json['subQuoteId'] as String?,
  subOrdertype: json['subOrdertype'] as String?,
  cancelQtty: json['cancelQtty'] as String?,
  errorMessage: json['errorMessage'] as String?,
  orderType: json['orderType'] as String?,
  activeType: json['activeType'] as String?,
);

Map<String, dynamic> _$VPDerConditionOrderDataToJson(
  VPDerConditionOrderData instance,
) => <String, dynamic>{
  'msgCode': instance.msgCode,
  'eventType': instance.eventType,
  'seq': instance.seq,
  'custodyCode': instance.custodyCode,
  'accountNo': instance.accountNo,
  'activeTime': instance.activeTime,
  'quoteId': instance.quoteId,
  'execType': instance.execType,
  'symbol': instance.symbol,
  'quoteQtty': instance.quoteQtty,
  'quotePrice': instance.quotePrice,
  'priceType': instance.priceType,
  'limitPrice': instance.limitPrice,
  'activePrice': instance.activePrice,
  'timeType': instance.timeType,
  'fromDate': instance.fromDate,
  'toDate': instance.toDate,
  'statusCode': instance.statusCode,
  'deltaValue': instance.deltaValue,
  'deltaType': instance.deltaType,
  'priceStep': instance.priceStep,
  'price': instance.price,
  'activePriceSL': instance.activePriceSL,
  'priceSL': instance.priceSL,
  'priceTP': instance.priceTP,
  'split': instance.split,
  'orderId': instance.orderId,
  'subQuoteId': instance.subQuoteId,
  'subOrdertype': instance.subOrdertype,
  'cancelQtty': instance.cancelQtty,
  'errorMessage': instance.errorMessage,
  'orderType': instance.orderType,
  'activeType': instance.activeType,
};
