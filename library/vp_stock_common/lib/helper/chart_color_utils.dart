import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/model/chart_data.dart';

class ChartColorUtils {
  static Color upColor = vpColor.strokeGreen;
  static Color downColor = vpColor.strokeDanger;
  static Color refColor = vpColor.strokeWarning;

  static Color shadowUpColor = vpColor.backgroundAccentGreen;
  static Color shadowDownColor = vpColor.backgroundAccentRed;
  static Color shadowRefColor = vpColor.backgroundAccentYellow;

  static Color getVolumeItemColor({
    required List<ChartData<dynamic>> data,
    required ChartData<dynamic>? item,
    required double reference,
  }) {
    if (item == null) return Colors.transparent;

    final index = data.indexOf(item);

    num prePrice = reference;

    if (index != -1 && index > 0) {
      prePrice = data[index - 1].value;
    }

    return item.value >= prePrice ? upColor : downColor;
  }

  static Color getHistoryChartColor({
    required num openPrice,
    required num closePrice,
  }) {
    return closePrice >= openPrice ? upColor : downColor;
  }
}
