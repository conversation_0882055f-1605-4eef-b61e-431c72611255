import 'dart:async';
import 'package:flutter/foundation.dart';

import 'package:vp_stock_common/vp_stock_common.dart';

mixin DerivativeOrdersSocketMixin {
  StreamSubscription? _subscription;

  final _socket =
      SocketFactory.get(SocketType.account) as VPSocketAccountConnect;

  void subscribeDerivativeOrderSocket() {
    _subscription = _socket.stream.listen(_parseData);
  }

  void _parseData(Map<String, dynamic> data) {
    if (data['type'] != 'der_condition_order') return;

    try {
      onDerivativeOrderListener(VPDerConditionOrderData.fromJson(data));
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  void unsubscribeDerivativeOrderSocket() {
    _subscription?.cancel();
    _subscription = null;
  }

  void onDerivativeOrderListener(VPDerConditionOrderData data) {}
}
